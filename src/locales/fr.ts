export const fr = {
  // Général
  common: {
    apply: 'Appliquer',
    cancel: 'Annuler',
    save: 'Enregistre<PERSON>',
    delete: 'Supprimer',
    edit: 'Modifier',
    update: 'Mettre à jour',
    reset: 'Réinitialiser',
    resetIcon: 'R<PERSON>initialiser l\'icône',
    add: 'Ajouter',
    remove: 'Supprimer',
    close: 'Fermer',
    open: 'Ouvrir',
    enable: 'Activer',
    disable: 'Désactiver',
    show: 'Afficher',
    hide: 'Masquer',
    yes: 'Oui',
    no: 'Non',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information',
    create: 'C<PERSON><PERSON>',
    enterName: 'Entrez un nom...',
    sidebar: 'Barre latérale',
    sidebarDescription: 'Barre latérale avec paramètres et contenu additionnel',
  },

  // Paramètres
  settings: {
    title: 'Paramètres',
    description: 'Ici se trouveront les paramètres de l\'extension (thème, apparence, options, etc.)',
    basicSettings: 'Paramètres de base',
    clockSettings: 'Paramètres de l\'horloge',
    searchSettings: 'Moteur de recherche',
    fastLinksSettings: 'Liens rapides',
    backgroundSettings: 'Arrière-plan',
    presetsSettings: 'Préréglages',
    listsSettings: 'Paramètres des listes',

    // Paramètres de base
    language: 'Langue de l\'interface',
    exportSettings: 'Exporter',
    importSettings: 'Importer',
    theme: 'Thème',
    accentColor: 'Couleur d\'accentuation',
    borderRadius: 'Rayon de la bordure',
    cleanMode: 'Mode épuré',
    cleanModeDescription: 'Masque les boutons d\'ouverture de liste',
    colorScheme: 'Schéma de couleurs',
    borderRounding: 'Arrondi des éléments',
    
    // Horloge
    showClock: 'Afficher l\'horloge',
    showSeconds: 'Afficher les secondes',
    showDate: 'Afficher la date',
    clockSize: 'Taille de l\'horloge',
    clockColor: 'Couleur de l\'horloge et de la date',
    
    // Recherche
    showSearch: 'Afficher la recherche',
    searchEngine: 'Moteur de recherche',
    searchSize: 'Taille de la recherche',
    searchBackgroundColor: 'Couleur de fond de la recherche',
    searchBorderColor: 'Couleur de bordure de la recherche',
    searchTextColor: 'Couleur du texte de la recherche',
    searchBackdropBlur: 'Flou d\'arrière-plan de la recherche',
    showSearchEngine: 'Afficher le moteur de recherche',
    backgroundBlur: 'Flou d\'arrière-plan',
    
    // Liens rapides
    showFastLinks: 'Afficher les liens rapides',
    fastLinksColumns: 'Nombre de colonnes',
    fastLinksSize: 'Taille du lien rapide',
    fastLinksBackdropBlur: 'Flou d\'arrière-plan du lien rapide',
    addFastLink: 'Ajouter un lien rapide',
    textColor: 'Couleur du texte du titre',
    backdropColor: 'Couleur de fond',
    iconBackgroundColor: 'Couleur de fond de l\'icône',
    colors: 'Couleurs',
    display: 'Affichage',
    hideIcons: 'Masquer les icônes',
    hideText: 'Masquer le texte',
    backgroundEffects: 'Effets d\'arrière-plan des icônes',

    // Listes
    showLists: 'Afficher les listes de liens',
    listsColumns: 'Nombre de colonnes',
    listsBackgroundColor: 'Couleur de fond des listes',
    listsBackdropBlur: 'Flou d\'arrière-plan des listes',
    addNewList: 'Ajouter une nouvelle liste',
    listBackground: 'Arrière-plan de la liste',
    backgroundColor: 'Couleur de fond',
    borderColor: 'Couleur de bordure',
    borderThickness: 'Épaisseur de la bordure',
    hideBorder: 'Masquer la bordure',
    hideBackground: 'Masquer l\'arrière-plan',
    separator: 'Séparateur',
    separatorColor: 'Couleur du séparateur',
    separatorThickness: 'Épaisseur du séparateur',
    hideSeparator: 'Masquer le séparateur',
    colorsAndIcons: 'Couleurs et icônes',
    titleColor: 'Couleur du titre',
    linkColor: 'Couleur du lien',
    hideLinkIcons: 'Masquer les icônes de lien',
    listGrid: 'Grille de liste',
    columnsCount: 'Nombre de colonnes',
    listManagement: 'Gestion des listes',
    
    // Arrière-plan
    backgroundType: 'Type d\'arrière-plan',
    solidColor: 'Couleur unie',
    gradient: 'Dégradé',
    image: 'Image',
    brightness: 'Luminosité',
    contrast: 'Contraste',
    saturation: 'Saturation',
    blur: 'Flou',
    hueRotate: 'Rotation de la teinte',
    sepia: 'Sépia',
    grayscale: 'Niveaux de gris',
    invert: 'Inverser',
    shadowOverlay: 'Superposition d\'ombre',
    parallaxEffect: 'Effet parallaxe',
    autoSwitch: 'Changement automatique',
    switchInterval: 'Intervalle de changement',
    addImages: 'Ajouter des images',
    uploadImages: 'Télécharger des images',
    addImage: 'Ajouter une image',
    checking: 'Vérification...',
    addRandomPhoto: 'Ajouter une photo aléatoire depuis internet',
    parallaxDescription: 'L\'image d\'arrière-plan suivra le mouvement de la souris',
    shadowBottom: 'Ombre du bas',
    shadowDescription: 'Ombre dégradée pour une meilleure visibilité des listes',
    intensity: 'Intensité :',
    height: 'Hauteur :',
    gallery: 'Galerie',
    startAutoSwitch: 'Démarrer le changement automatique',
    stopAutoSwitch: 'Arrêter le changement automatique',
    onLoad: 'Au chargement',
    daily: 'Quotidien',
    deleteImage: 'Supprimer l\'image',
    
    // Dégradés
    gradientType: 'Type de dégradé',
    addColor: 'Ajouter une couleur',
    deleteColor: 'Supprimer une couleur',
    direction: 'Direction',
    position: 'Position',
    customCSS: 'Dégradé CSS (optionnel)',
    customCSSDescription: 'Entrez la chaîne de dégradé CSS à appliquer au lieu de la configuration manuelle',
    backgroundFilters: 'Filtres d\'arrière-plan',
    resetFilters: 'Réinitialiser les filtres',
    expandFilters: 'Développer les filtres',
    collapseFilters: 'Réduire les filtres',
    color: 'Couleur',
    filters: 'Filtres d\'arrière-plan',
    font: 'Police',
    right: 'Droite',
    left: 'Gauche',
    bottom: 'Bas',
    top: 'Haut',
    bottomRight: 'En bas à droite',
    bottomLeft: 'En bas à gauche',
    topRight: 'En haut à droite',
    topLeft: 'En haut à gauche',
    center: 'Centre',
    gradientDirection: 'Direction du dégradé',
    gradientColor1: 'Couleur 1',
    gradientColor2: 'Couleur 2',
    gradientColor3: 'Couleur 3',
    gradientColor4: 'Couleur 4',
    gradientColor5: 'Couleur 5',
    gradientColor6: 'Couleur 6',
    customGradient: 'Dégradé personnalisé',
    customGradientCSS: 'CSS personnalisé',
    gradientPreview: 'Aperçu',
    linear: 'Linéaire',
    radial: 'Radial',
    conic: 'Conique',
    toRight: 'Vers la droite',
    toLeft: 'Vers la gauche',
    toBottom: 'Vers le bas',
    toTop: 'Vers le haut',
    toBottomRight: 'Vers le bas à droite',
    toBottomLeft: 'Vers le bas à gauche',
    toTopRight: 'Vers le haut à droite',
    toTopLeft: 'Vers le haut à gauche',
    circle: 'Cercle',
    ellipse: 'Ellipse',
    fromCenter: 'Depuis le centre',
    
    // Filtres d'image
    imageFilters: 'Filtres d\'image',
    
    // Préréglages
    presets: 'Préréglages',
    createPreset: 'Créer un préréglage',
    presetName: 'Nom du préréglage',
    noPresets: 'Aucun préréglage créé. Créez votre premier préréglage pour basculer rapidement entre les paramètres.',
    renamePreset: 'Renommer le préréglage',
    updatePreset: 'Mettre à jour le préréglage avec les paramètres actuels',
    deletePreset: 'Supprimer le préréglage',
    createNewPreset: 'Créer un nouveau préréglage',
    presetDescription: 'Le préréglage sauvegardera les paramètres actuels de couleur, de police et d\'arrière-plan.',

    // Catégories de polices
    fontCategories: {
      sansSerif: 'Sans empattement',
      serif: 'Avec empattement',
      monospace: 'Monospace',
      display: 'Affichage',
      handwriting: 'Écriture manuscrite',
      pixel: 'Pixel',
      terminal: 'Terminal',
    },
    
    // Traductions
    aiTranslationsDisclaimer: 'Toutes les traductions sont générées par IA',

    // Noms et catégories de polices
    fonts: {
      systemFont: 'Police système',
      systemFonts: 'Polices système',
      serifFonts: 'Polices avec empattement',
      monospaceFonts: 'Polices monospace',
      displayFonts: 'Polices d\'affichage',
      pixelFonts: 'Polices pixel',
      terminalFonts: 'Polices terminal',
      modernFonts: 'Polices modernes additionnelles',
      decorativeFonts: 'Polices décoratives additionnelles',
    },

    // Réinitialisation et exportation
    resetAllColors: 'Réinitialiser toutes les couleurs à l\'accentuation',
    resetAllSettings: 'Réinitialiser tous les paramètres',
  },

  // Listes
  lists: {
    newList: 'Nouvelle liste',
    listName: 'Nom de la liste',
    addLink: 'Ajouter un lien',
    linkName: 'Nom du lien',
    linkUrl: 'URL du lien',
    editList: 'Modifier la liste',
    deleteList: 'Supprimer la liste',
    listIcon: 'Icône de la liste',
    listColor: 'Couleur de la liste',
    linkColor: 'Couleur du lien',
    hideIcons: 'Masquer les icônes de lien',
    openInNewWindow: 'Ouvrir dans une nouvelle fenêtre',
    copyLink: 'Copier le lien',
    editLink: 'Modifier le lien',
    deleteLink: 'Supprimer le lien',
    title: 'Titre',
    url: 'URL',
    addNewList: 'Ajouter une liste',
    iconColor: 'Couleur de l\'icône',
  },

  // Liens rapides
  fastLinks: {
    newFastLink: 'Nouveau lien rapide',
    fastLinkName: 'Nom du lien rapide',
    fastLinkUrl: 'URL',
    editFastLink: 'Modifier le lien rapide',
    deleteFastLink: 'Supprimer le lien rapide',
    fastLinkColor: 'Couleur du lien rapide',
  },

  // Recherche
  search: {
    placeholder: 'Rechercher...',
    searchWith: 'Rechercher avec',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Rechercher des polices...',
    fontsNotFound: 'Polices non trouvées',
    searchInGoogle: 'Rechercher dans Google...',
    searchInYandex: 'Rechercher dans Yandex...',
    searchInBing: 'Rechercher dans Bing...',
    searchInDuckDuckGo: 'Rechercher dans DuckDuckGo...',
    searchInYahoo: 'Rechercher dans Yahoo...',
    searchInBaidu: 'Rechercher dans Baidu...',
    searchInStartpage: 'Rechercher dans Startpage...',
    searchInSearX: 'Rechercher dans SearX...',
    searchInEcosia: 'Rechercher dans Ecosia...',
    searchInBrave: 'Rechercher dans Brave...',
  },

  // Erreurs
  errors: {
    invalidUrl: 'URL invalide',
    nameRequired: 'Le nom est requis',
    urlRequired: 'L\'URL est requise',
    fileUploadError: 'Erreur de téléchargement de fichier',
    settingsImportError: 'Erreur d\'importation des paramètres',
    settingsExportError: 'Erreur d\'exportation des paramètres',
    invalidImageUrl: 'URL d\'image invalide',
    imageValidationError: 'Erreur de validation de l\'image',
    imageLoadFailed: 'Échec du chargement de l\'image',
    imageTooSmall: 'L\'image est trop petite',
    settingsImported: 'Paramètres importés avec succès ! La page sera rechargée pour appliquer toutes les modifications.',
    settingsExported: 'Paramètres exportés avec succès',
    parseError: 'Erreur d\'analyse :',
    invalidFileFormat: 'Erreur de lecture du fichier de paramètres. Assurez-vous que le fichier a le bon format.',
    importError: 'Erreur lors de l\'importation des paramètres. Vérifiez la console pour plus de détails.',
    exportError: 'Erreur lors de l\'exportation des paramètres. Vérifiez la console pour plus de détails.',
    resetConfirm: 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ? Cette action est irréversible. Tous les paramètres personnalisés, y compris les arrière-plans, les listes et les liens rapides, seront complètement supprimés du stockage local.',
    deleteListConfirm: 'Êtes-vous sûr de vouloir supprimer cette liste ? Cette action est irréversible.',
    deleteLinkConfirm: 'Êtes-vous sûr de vouloir supprimer ce lien ?',
    loadingError: 'Erreur de chargement',
    backgroundLoadError: 'Échec du chargement de l\'image d\'arrière-plan',
    criticalError: 'Une erreur critique est survenue. La page va être rechargée.',
    jsError: 'Erreur JavaScript',
    promiseRejection: 'Erreur de promesse non gérée',
  },

  // Infobulles
  tooltips: {
    settings: 'Paramètres',
    addList: 'Ajouter une liste',
    addFastLink: 'Ajouter un lien rapide',
    editItem: 'Modifier',
    deleteItem: 'Supprimer',
    updateItem: 'Mettre à jour',
    resetColor: 'Réinitialiser la couleur',
    openAllLinks: 'Ouvrir tous les liens dans de nouveaux onglets',
    addLink: 'Ajouter un lien',
    openLink: 'Ouvrir le lien',
    copyLink: 'Copier le lien',
    dragToReorder: 'Glisser pour réorganiser',
    exportSettings: 'Exporter tous les paramètres vers un fichier',
    importSettings: 'Importer les paramètres depuis un fichier',
    resetAllSettings: 'Réinitialiser tous les paramètres',
    closeSettings: 'Fermer les paramètres',
    generateColor: 'Générer une couleur basée sur l\'image',
    applyAccentColor: 'Appliquer la couleur d\'accentuation à tous les éléments',
    addRandomPhoto: 'Ajouter une photo aléatoire depuis internet',
    deleteImage: 'Supprimer l\'image',
    addColor: 'Ajouter une couleur',
    deleteColor: 'Supprimer une couleur',
    resetFilters: 'Réinitialiser les filtres',
    expandFilters: 'Développer les filtres',
    collapseFilters: 'Réduire les filtres',
    stopAutoSwitch: 'Arrêter le changement automatique',
    startAutoSwitch: 'Démarrer le changement automatique',
  },

  // Heure
  time: {
    seconds: 's',
    minutes: 'min',
    hours: 'h',
    days: 'jours',
    months: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
    weekdays: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
  },

  // Unités
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 's',
    minutes: 'min',
    small: 'Petit',
    medium: 'Moyen',
    large: 'Grand',
  },

  // Boîtes de dialogue
  dialogs: {
    newName: 'Nouveau nom',
    newListName: 'Nom de la nouvelle liste',
    linkTitle: 'Titre du lien',
    linkColor: 'Couleur du lien',
    separatorColor: 'Couleur du séparateur',
    titleColor: 'Couleur du titre',
  },

  // Étiquettes ARIA
  ariaLabels: {
    settings: 'Paramètres',
    resetAllSettings: 'Réinitialiser tous les paramètres',
    closeSettings: 'Fermer les paramètres',
    applyAccentColor: 'Appliquer la couleur d\'accentuation à tous les éléments',
    addList: 'Ajouter une liste',
    addRandomPhoto: 'Ajouter une photo aléatoire',
    deleteImage: 'Supprimer l\'image',
    addColor: 'Ajouter une couleur',
    deleteColor: 'Supprimer une couleur',
    resetFilters: 'Réinitialiser les filtres',
    expandFilters: 'Développer les filtres',
    collapseFilters: 'Réduire les filtres',
  },

  // Rayon de bordure
  radius: {
    none: 'Aucun',
    small: 'Petit',
    medium: 'Moyen',
    large: 'Grand',
    full: 'Complet',
  },

  // Catégories de polices
  fonts: {
    system: 'Système',
    serif: 'Serif',
    monospace: 'Monospace',
    display: 'Affichage',
    pixel: 'Pixel',
    terminal: 'Terminal',
    modern: 'Moderne',
    decorative: 'Décoratif',
  },
};