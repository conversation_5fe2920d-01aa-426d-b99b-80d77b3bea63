export const ar = {
  // عام
  common: {
    apply: 'تطبيق',
    cancel: 'إلغاء',
    save: 'حفظ',
    delete: 'حذف',
    edit: 'تعديل',
    update: 'تحديث',
    reset: 'إعادة تعيين',
    resetIcon: 'إعادة تعيين الأيقونة',
    add: 'إضافة',
    remove: 'إزالة',
    close: 'إغلاق',
    open: 'فتح',
    enable: 'تمكين',
    disable: 'تعطيل',
    show: 'إظهار',
    hide: 'إخفاء',
    yes: 'نعم',
    no: 'لا',
    loading: 'جار التحميل...',
    error: 'خطأ',
    success: 'نجاح',
    warning: 'تحذير',
    info: 'معلومات',
    create: 'إنشاء',
    enterName: 'أدخل الاسم...',
    sidebar: 'الشريط الجانبي',
    sidebarDescription: 'شريط جانبي به إعدادات ومحتوى إضافي',
  },

  // الإعدادات
  settings: {
    title: 'الإعدادات',
    description: 'هنا ستكون إعدادات الإضافة (السمة، المظهر، الخيارات، إلخ.)',
    basicSettings: 'الإعدادات الأساسية',
    clockSettings: 'إعدادات الساعة',
    searchSettings: 'محرك البحث',
    fastLinksSettings: 'الروابط السريعة',
    backgroundSettings: 'الخلفية',
    presetsSettings: 'الإعدادات المسبقة',
    listsSettings: 'إعدادات القوائم',

    // الإعدادات الأساسية
    language: 'لغة الواجهة',
    exportSettings: 'تصدير',
    importSettings: 'استيراد',
    theme: 'السمة',
    accentColor: 'لون التمييز',
    borderRadius: 'نصف قطر الحدود',
    cleanMode: 'الوضع النظيف',
    cleanModeDescription: 'يخفي أزرار فتح القائمة',
    colorScheme: 'نظام الألوان',
    borderRounding: 'استدارة العناصر',
    
    // الساعة
    showClock: 'إظهار الساعة',
    showSeconds: 'إظهار الثواني',
    showDate: 'إظهار التاريخ',
    clockSize: 'حجم الساعة',
    clockColor: 'لون الساعة والتاريخ',
    
    // البحث
    showSearch: 'إظهار البحث',
    searchEngine: 'محرك البحث',
    searchSize: 'حجم البحث',
    searchBackgroundColor: 'لون خلفية البحث',
    searchBorderColor: 'لون حدود البحث',
    searchTextColor: 'لون نص البحث',
    searchBackdropBlur: 'تمويه خلفية البحث',
    showSearchEngine: 'إظهار محرك البحث',
    backgroundBlur: 'تمويه الخلفية',
    
    // الروابط السريعة
    showFastLinks: 'إظهار الروابط السريعة',
    fastLinksColumns: 'عدد الأعمدة',
    fastLinksSize: 'حجم الرابط السريع',
    fastLinksBackdropBlur: 'تمويه خلفية الرابط السريع',
    addFastLink: 'إضافة رابط سريع',
    textColor: 'لون نص العنوان',
    backdropColor: 'لون الخلفية',
    iconBackgroundColor: 'لون خلفية الأيقونة',
    colors: 'الألوان',
    display: 'العرض',
    hideIcons: 'إخفاء الأيقونات',
    hideText: 'إخفاء النص',
    backgroundEffects: 'تأثيرات خلفية الأيقونة',

    // القوائم
    showLists: 'إظهار قوائم الروابط',
    listsColumns: 'عدد الأعمدة',
    listsBackgroundColor: 'لون خلفية القوائم',
    listsBackdropBlur: 'تمويه خلفية القوائم',
    addNewList: 'إضافة قائمة جديدة',
    listBackground: 'خلفية القائمة',
    backgroundColor: 'لون الخلفية',
    borderColor: 'لون الحدود',
    borderThickness: 'سُمك الحدود',
    hideBorder: 'إخفاء الحدود',
    hideBackground: 'إخفاء الخلفية',
    separator: 'فاصل',
    separatorColor: 'لون الفاصل',
    separatorThickness: 'سُمك الفاصل',
    hideSeparator: 'إخفاء الفاصل',
    colorsAndIcons: 'الألوان والأيقونات',
    titleColor: 'لون العنوان',
    linkColor: 'لون الرابط',
    hideLinkIcons: 'إخفاء أيقونات الرابط',
    listGrid: 'شبكة القائمة',
    columnsCount: 'عدد الأعمدة',
    listManagement: 'إدارة القوائم',
    
    // الخلفية
    backgroundType: 'نوع الخلفية',
    solidColor: 'لون خالص',
    gradient: 'تدرج',
    image: 'صورة',
    brightness: 'السطوع',
    contrast: 'التباين',
    saturation: 'التشبع',
    blur: 'التمويه',
    hueRotate: 'تدوير اللون',
    sepia: 'بني داكن',
    grayscale: 'تدرج الرمادي',
    invert: 'عكس الألوان',
    shadowOverlay: 'تراكب الظل',
    parallaxEffect: 'تأثير المنظر',
    autoSwitch: 'تبديل تلقائي',
    switchInterval: 'فاصل التبديل',
    addImages: 'إضافة صور',
    uploadImages: 'تحميل صور',
    addImage: 'إضافة صورة',
    checking: 'جار التحقق...',
    addRandomPhoto: 'إضافة صورة عشوائية من الإنترنت',
    parallaxDescription: 'ستتبع صورة الخلفية حركة الماوس',
    shadowBottom: 'الظل السفلي',
    shadowDescription: 'ظل متدرج لرؤية أفضل للقائمة',
    intensity: 'الشدة:',
    height: 'الارتفاع:',
    gallery: 'المعرض',
    startAutoSwitch: 'بدء التبديل التلقائي',
    stopAutoSwitch: 'إيقاف التبديل التلقائي',
    onLoad: 'عند التحميل',
    daily: 'يوميًا',
    deleteImage: 'حذف الصورة',
    
    // التدرجات
    gradientType: 'نوع التدرج',
    addColor: 'إضافة لون',
    deleteColor: 'حذف اللون',
    direction: 'الاتجاه',
    position: 'الموضع',
    customCSS: 'تدرج CSS (اختياري)',
    customCSSDescription: 'أدخل سلسلة تدرج CSS لتطبيقها بدلاً من الإعداد اليدوي',
    backgroundFilters: 'فلاتر الخلفية',
    resetFilters: 'إعادة تعيين الفلاتر',
    expandFilters: 'توسيع الفلاتر',
    collapseFilters: 'طي الفلاتر',
    color: 'اللون',
    filters: 'فلاتر الخلفية',
    font: 'الخط',
    right: 'يمين',
    left: 'يسار',
    bottom: 'أسفل',
    top: 'أعلى',
    bottomRight: 'أسفل يمين',
    bottomLeft: 'أسفل يسار',
    topRight: 'أعلى يمين',
    topLeft: 'أعلى يسار',
    center: 'المركز',
    gradientDirection: 'اتجاه التدرج',
    gradientColor1: 'اللون 1',
    gradientColor2: 'اللون 2',
    gradientColor3: 'اللون 3',
    gradientColor4: 'اللون 4',
    gradientColor5: 'اللون 5',
    gradientColor6: 'اللون 6',
    customGradient: 'تدرج مخصص',
    customGradientCSS: 'CSS مخصص',
    gradientPreview: 'معاينة',
    linear: 'خطي',
    radial: 'شعاعي',
    conic: 'مخروطي',
    toRight: 'إلى اليمين',
    toLeft: 'إلى اليسار',
    toBottom: 'إلى الأسفل',
    toTop: 'إلى الأعلى',
    toBottomRight: 'إلى الأسفل اليمين',
    toBottomLeft: 'إلى الأسفل اليسار',
    toTopRight: 'إلى الأعلى اليمين',
    toTopLeft: 'إلى الأعلى اليسار',
    circle: 'دائرة',
    ellipse: 'قطع ناقص',
    fromCenter: 'من المركز',
    
    // فلاتر الصور
    imageFilters: 'فلاتر الصور',
    
    // الإعدادات المسبقة
    presets: 'الإعدادات المسبقة',
    createPreset: 'إنشاء إعداد مسبق',
    presetName: 'اسم الإعداد المسبق',
    noPresets: 'لم يتم إنشاء إعدادات مسبقة. أنشئ إعدادك المسبق الأول للتبديل السريع بين الإعدادات.',
    renamePreset: 'إعادة تسمية الإعداد المسبق',
    updatePreset: 'تحديث الإعداد المسبق بالإعدادات الحالية',
    deletePreset: 'حذف الإعداد المسبق',
    createNewPreset: 'إنشاء إعداد مسبق جديد',
    presetDescription: 'سيحفظ الإعداد المسبق إعدادات اللون والخط والخلفية الحالية.',

    // فئات الخطوط
    fontCategories: {
      sansSerif: 'بلا سريف',
      serif: 'سريف',
      monospace: 'أحادي المسافة',
      display: 'عرض',
      handwriting: 'خط يد',
      pixel: 'بكسل',
      terminal: 'طرفي',
    },
    
    // الترجمات
    aiTranslationsDisclaimer: 'جميع الترجمات تم إنشاؤها بواسطة الذكاء الاصطناعي',

    // أسماء وفئات الخطوط
    fonts: {
      systemFont: 'خط النظام',
      systemFonts: 'خطوط النظام',
      serifFonts: 'خطوط سريف',
      monospaceFonts: 'خطوط أحادية المسافة',
      displayFonts: 'خطوط عرض',
      pixelFonts: 'خطوط بكسل',
      terminalFonts: 'خطوط طرفية',
      modernFonts: 'خطوط حديثة إضافية',
      decorativeFonts: 'خطوط زخرفية إضافية',
    },

    // إعادة تعيين وتصدير
    resetAllColors: 'إعادة تعيين جميع الألوان إلى لون التمييز',
    resetAllSettings: 'إعادة تعيين جميع الإعدادات',
  },

  // القوائم
  lists: {
    newList: 'قائمة جديدة',
    listName: 'اسم القائمة',
    addLink: 'إضافة رابط',
    linkName: 'اسم الرابط',
    linkUrl: 'عنوان URL للرابط',
    editList: 'تعديل القائمة',
    deleteList: 'حذف القائمة',
    listIcon: 'أيقونة القائمة',
    listColor: 'لون القائمة',
    linkColor: 'لون الرابط',
    hideIcons: 'إخفاء أيقونات الرابط',
    openInNewWindow: 'فتح في نافذة جديدة',
    copyLink: 'نسخ الرابط',
    editLink: 'تعديل الرابط',
    deleteLink: 'حذف الرابط',
    title: 'العنوان',
    url: 'عنوان URL',
    addNewList: 'إضافة قائمة',
    iconColor: 'لون الأيقونة',
  },

  // الروابط السريعة
  fastLinks: {
    newFastLink: 'رابط سريع جديد',
    fastLinkName: 'اسم الرابط السريع',
    fastLinkUrl: 'عنوان URL',
    editFastLink: 'تعديل الرابط السريع',
    deleteFastLink: 'حذف الرابط السريع',
    fastLinkColor: 'لون الرابط السريع',
  },

  // البحث
  search: {
    placeholder: 'بحث...',
    searchWith: 'البحث باستخدام',
    google: 'جوجل',
    yandex: 'ياندكس',
    bing: 'بينج',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'البحث عن الخطوط...',
    fontsNotFound: 'لم يتم العثور على خطوط',
    searchInGoogle: 'البحث في جوجل...',
    searchInYandex: 'البحث في ياندكس...',
    searchInBing: 'البحث في بينج...',
    searchInDuckDuckGo: 'البحث في DuckDuckGo...',
    searchInYahoo: 'البحث في ياهو...',
    searchInBaidu: 'البحث في بايدو...',
    searchInStartpage: 'البحث في ستارت بيج...',
    searchInSearX: 'البحث في سير إكس...',
    searchInEcosia: 'البحث في إكوسيا...',
    searchInBrave: 'البحث في بريف...',
  },

  // الأخطاء
  errors: {
    invalidUrl: 'عنوان URL غير صالح',
    nameRequired: 'الاسم مطلوب',
    urlRequired: 'عنوان URL مطلوب',
    fileUploadError: 'خطأ في تحميل الملف',
    settingsImportError: 'خطأ في استيراد الإعدادات',
    settingsExportError: 'خطأ في تصدير الإعدادات',
    invalidImageUrl: 'عنوان URL للصورة غير صالح',
    imageValidationError: 'خطأ في التحقق من صحة الصورة',
    imageLoadFailed: 'فشل تحميل الصورة',
    imageTooSmall: 'الصورة صغيرة جدًا',
    settingsImported: 'تم استيراد الإعدادات بنجاح! سيتم إعادة تحميل الصفحة لتطبيق جميع التغييرات.',
    settingsExported: 'تم تصدير الإعدادات بنجاح',
    parseError: 'خطأ في التحليل:',
    invalidFileFormat: 'خطأ في قراءة ملف الإعدادات. تأكد من أن الملف بالصيغة الصحيحة.',
    importError: 'خطأ في استيراد الإعدادات. تحقق من وحدة التحكم للحصول على التفاصيل.',
    exportError: 'خطأ في تصدير الإعدادات. تحقق من وحدة التحكم للحصول على التفاصيل.',
    resetConfirm: 'هل أنت متأكد أنك تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟ لا يمكن التراجع عن هذا الإجراء. ستتم إزالة جميع الإعدادات المخصصة، بما في ذلك الخلفيات والقوائم والروابط السريعة، بالكامل من التخزين المحلي.',
    deleteListConfirm: 'هل أنت متأكد أنك تريد حذف هذه القائمة؟ لا يمكن التراجع عن هذا الإجراء.',
    deleteLinkConfirm: 'هل أنت متأكد أنك تريد حذف هذا الرابط؟',
    loadingError: 'خطأ في التحميل',
    backgroundLoadError: 'فشل تحميل صورة الخلفية',
    criticalError: 'حدث خطأ فادح. سيتم إعادة تحميل الصفحة.',
    jsError: 'خطأ JavaScript',
    promiseRejection: 'خطأ Promise غير معالج',
  },

  // تلميحات الأدوات
  tooltips: {
    settings: 'الإعدادات',
    addList: 'إضافة قائمة',
    addFastLink: 'إضافة رابط سريع',
    editItem: 'تعديل',
    deleteItem: 'حذف',
    updateItem: 'تحديث',
    resetColor: 'إعادة تعيين اللون',
    openAllLinks: 'فتح جميع الروابط في علامات تبويب جديدة',
    addLink: 'إضافة رابط',
    openLink: 'فتح الرابط',
    copyLink: 'نسخ الرابط',
    dragToReorder: 'اسحب لإعادة الترتيب',
    exportSettings: 'تصدير جميع الإعدادات إلى ملف',
    importSettings: 'استيراد الإعدادات من ملف',
    resetAllSettings: 'إعادة تعيين جميع الإعدادات',
    closeSettings: 'إغلاق الإعدادات',
    generateColor: 'توليد لون بناءً على الصورة',
    applyAccentColor: 'تطبيق لون التمييز على جميع العناصر',
    addRandomPhoto: 'إضافة صورة عشوائية من الإنترنت',
    deleteImage: 'حذف الصورة',
    addColor: 'إضافة لون',
    deleteColor: 'حذف اللون',
    resetFilters: 'إعادة تعيين الفلاتر',
    expandFilters: 'توسيع الفلاتر',
    collapseFilters: 'طي الفلاتر',
    stopAutoSwitch: 'إيقاف التبديل التلقائي',
    startAutoSwitch: 'بدء التبديل التلقائي',
  },

  // الوقت
  time: {
    seconds: 'ث',
    minutes: 'دقيقة',
    hours: 'ساعة',
    days: 'أيام',
    months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
    weekdays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
  },

  // الوحدات
  units: {
    pixels: 'بكسل',
    percent: '%',
    seconds: 'ث',
    minutes: 'دقيقة',
    small: 'صغير',
    medium: 'متوسط',
    large: 'كبير',
  },

  // مربعات الحوار
  dialogs: {
    newName: 'اسم جديد',
    newListName: 'اسم قائمة جديد',
    linkTitle: 'عنوان الرابط',
    linkColor: 'لون الرابط',
    separatorColor: 'لون الفاصل',
    titleColor: 'لون العنوان',
  },

  // تسميات ARIA
  ariaLabels: {
    settings: 'الإعدادات',
    resetAllSettings: 'إعادة تعيين جميع الإعدادات',
    closeSettings: 'إغلاق الإعدادات',
    applyAccentColor: 'تطبيق لون التمييز على جميع العناصر',
    addList: 'إضافة قائمة',
    addRandomPhoto: 'إضافة صورة عشوائية',
    deleteImage: 'حذف الصورة',
    addColor: 'إضافة لون',
    deleteColor: 'حذف اللون',
    resetFilters: 'إعادة تعيين الفلاتر',
    expandFilters: 'توسيع الفلاتر',
    collapseFilters: 'طي الفلاتر',
  },

  // نصف قطر الحدود
  radius: {
    none: 'لا شيء',
    small: 'صغير',
    medium: 'متوسط',
    large: 'كبير',
    full: 'كامل',
  },

  // فئات الخطوط
  fonts: {
    system: 'النظام',
    serif: 'سريف',
    monospace: 'أحادي المسافة',
    display: 'عرض',
    pixel: 'بكسل',
    terminal: 'طرفي',
    modern: 'حديث',
    decorative: 'زخرفي',
  },
};