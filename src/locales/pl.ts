export const pl = {
  // General
  common: {
    apply: 'Zast<PERSON>uj',
    cancel: '<PERSON>ulu<PERSON>',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON>u<PERSON>',
    edit: 'Edytuj',
    update: 'Zak<PERSON>alizuj',
    reset: 'Resetuj',
    resetIcon: 'Resetuj ikonę',
    add: 'Doda<PERSON>',
    remove: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON>kni<PERSON>',
    open: '<PERSON>t<PERSON><PERSON><PERSON>',
    enable: '<PERSON><PERSON><PERSON><PERSON>',
    disable: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    show: '<PERSON><PERSON><PERSON>',
    hide: '<PERSON>kryj',
    yes: 'Tak',
    no: 'Nie',
    loading: 'Ładowanie...',
    error: 'Błąd',
    success: 'Suk<PERSON>',
    warning: 'Ostrzeżenie',
    info: 'Informacje',
    create: 'Utw<PERSON><PERSON>',
    enterName: 'Wpisz nazwę...',
    sidebar: 'Pasek boczny',
    sidebarDescription: 'Pasek boczny z ustawieniami i dodatkową zawartością',
  },

  // Settings
  settings: {
    title: 'Usta<PERSON>enia',
    description: '<PERSON><PERSON><PERSON> będą ustawienia rozszerzenia (motyw, wygląd, opcje itp.)',
    basicSettings: 'Ustawienia podstawowe',
    clockSettings: 'Ustawienia zegara',
    searchSettings: 'Wyszukiwarka',
    fastLinksSettings: 'Szybkie linki',
    backgroundSettings: 'Tło',
    presetsSettings: 'Predefiniowane ustawienia',
    listsSettings: 'Ustawienia list',

    // Basic settings
    language: 'Język interfejsu',
    exportSettings: 'Eksportuj',
    importSettings: 'Importuj',
    theme: 'Motyw',
    accentColor: 'Kolor akcentu',
    borderRadius: 'Promień zaokrąglenia',
    cleanMode: 'Tryb czysty',
    cleanModeDescription: 'Ukrywa przyciski otwierania list',
    colorScheme: 'Schemat kolorów',
    borderRounding: 'Zaokrąglenie elementów',
    
    // Clock
    showClock: 'Pokaż zegar',
    showSeconds: 'Pokaż sekundy',
    showDate: 'Pokaż datę',
    clockSize: 'Rozmiar zegara',
    clockColor: 'Kolor zegara i daty',
    
    // Search
    showSearch: 'Pokaż wyszukiwarkę',
    searchEngine: 'Wyszukiwarka',
    searchSize: 'Rozmiar wyszukiwarki',
    searchBackgroundColor: 'Kolor tła wyszukiwarki',
    searchBorderColor: 'Kolor obramowania wyszukiwarki',
    searchTextColor: 'Kolor tekstu wyszukiwarki',
    searchBackdropBlur: 'Rozmycie tła wyszukiwarki',
    showSearchEngine: 'Pokaż wyszukiwarkę',
    backgroundBlur: 'Rozmycie tła',
    
    // Quick links
    showFastLinks: 'Pokaż szybkie linki',
    fastLinksColumns: 'Liczba kolumn',
    fastLinksSize: 'Rozmiar szybkiego linku',
    fastLinksBackdropBlur: 'Rozmycie tła szybkiego linku',
    addFastLink: 'Dodaj szybki link',
    textColor: 'Kolor tekstu tytułu',
    backdropColor: 'Kolor tła',
    iconBackgroundColor: 'Kolor tła ikony',
    colors: 'Kolory',
    display: 'Wyświetlanie',
    hideIcons: 'Ukryj ikony',
    hideText: 'Ukryj tekst',
    backgroundEffects: 'Efekty tła ikony',

    // Lists
    showLists: 'Pokaż listy linków',
    listsColumns: 'Liczba kolumn',
    listsBackgroundColor: 'Kolor tła list',
    listsBackdropBlur: 'Rozmycie tła list',
    addNewList: 'Dodaj nową listę',
    listBackground: 'Tło listy',
    backgroundColor: 'Kolor tła',
    borderColor: 'Kolor obramowania',
    borderThickness: 'Grubość obramowania',
    hideBorder: 'Ukryj obramowanie',
    hideBackground: 'Ukryj tło',
    separator: 'Separator',
    separatorColor: 'Kolor separatora',
    separatorThickness: 'Grubość separatora',
    hideSeparator: 'Ukryj separator',
    colorsAndIcons: 'Kolory i ikony',
    titleColor: 'Kolor tytułu',
    linkColor: 'Kolor linku',
    hideLinkIcons: 'Ukryj ikony linków',
    listGrid: 'Siatka listy',
    columnsCount: 'Liczba kolumn',
    listManagement: 'Zarządzanie listami',
    
    // Background
    backgroundType: 'Typ tła',
    solidColor: 'Jednolity kolor',
    gradient: 'Gradient',
    image: 'Obraz',
    brightness: 'Jasność',
    contrast: 'Kontrast',
    saturation: 'Nasycenie',
    blur: 'Rozmycie',
    hueRotate: 'Obrót odcienia',
    sepia: 'Sepia',
    grayscale: 'Skala szarości',
    invert: 'Inwertuj',
    shadowOverlay: 'Nakładka cienia',
    parallaxEffect: 'Efekt paralaksy',
    autoSwitch: 'Automatyczne przełączanie',
    switchInterval: 'Interwał przełączania',
    addImages: 'Dodaj obrazy',
    uploadImages: 'Prześlij obrazy',
    addImage: 'Dodaj obraz',
    checking: 'Sprawdzanie...',
    addRandomPhoto: 'Dodaj losowe zdjęcie z internetu',
    parallaxDescription: 'Obraz tła będzie podążał za ruchem myszy',
    shadowBottom: 'Cień dolny',
    shadowDescription: 'Gradientowy cień dla lepszej widoczności listy',
    intensity: 'Intensywność:',
    height: 'Wysokość:',
    gallery: 'Galeria',
    startAutoSwitch: 'Rozpocznij automatyczne przełączanie',
    stopAutoSwitch: 'Zatrzymaj automatyczne przełączanie',
    onLoad: 'Przy ładowaniu',
    daily: 'Codziennie',
    deleteImage: 'Usuń obraz',
    
    // Gradients
    gradientType: 'Typ gradientu',
    addColor: 'Dodaj kolor',
    deleteColor: 'Usuń kolor',
    direction: 'Kierunek',
    position: 'Pozycja',
    customCSS: 'Gradient CSS (opcjonalnie)',
    customCSSDescription: 'Wprowadź ciąg gradientu CSS, aby zastosować zamiast ręcznej konfiguracji',
    backgroundFilters: 'Filtry tła',
    resetFilters: 'Resetuj filtry',
    expandFilters: 'Rozwiń filtry',
    collapseFilters: 'Zwiń filtry',
    color: 'Kolor',
    filters: 'Filtry tła',
    font: 'Czcionka',
    right: 'Prawo',
    left: 'Lewo',
    bottom: 'Dół',
    top: 'Góra',
    bottomRight: 'Prawy dół',
    bottomLeft: 'Lewy dół',
    topRight: 'Prawy góra',
    topLeft: 'Lewy góra',
    center: 'Środek',
    gradientDirection: 'Kierunek gradientu',
    gradientColor1: 'Kolor 1',
    gradientColor2: 'Kolor 2',
    gradientColor3: 'Kolor 3',
    gradientColor4: 'Kolor 4',
    gradientColor5: 'Kolor 5',
    gradientColor6: 'Kolor 6',
    customGradient: 'Niestandardowy gradient',
    customGradientCSS: 'Niestandardowy CSS',
    gradientPreview: 'Podgląd',
    linear: 'Liniowy',
    radial: 'Promienisty',
    conic: 'Stożkowy',
    toRight: 'W prawo',
    toLeft: 'W lewo',
    toBottom: 'W dół',
    toTop: 'W górę',
    toBottomRight: 'W dół w prawo',
    toBottomLeft: 'W dół w lewo',
    toTopRight: 'W górę w prawo',
    toTopLeft: 'W górę w lewo',
    circle: 'Koło',
    ellipse: 'Elipsa',
    fromCenter: 'Od środka',
    
    // Image filters
    imageFilters: 'Filtry obrazu',
    
    // Presets
    presets: 'Predefiniowane ustawienia',
    createPreset: 'Utwórz predefiniowane ustawienie',
    presetName: 'Nazwa predefiniowanego ustawienia',
    noPresets: 'Brak utworzonych predefiniowanych ustawień. Utwórz swoje pierwsze predefiniowane ustawienie, aby szybko przełączać się między ustawieniami.',
    renamePreset: 'Zmień nazwę predefiniowanego ustawienia',
    updatePreset: 'Zaktualizuj predefiniowane ustawienie bieżącymi ustawieniami',
    deletePreset: 'Usuń predefiniowane ustawienie',
    createNewPreset: 'Utwórz nowe predefiniowane ustawienie',
    presetDescription: 'Predefiniowane ustawienie zapisze bieżące ustawienia koloru, czcionki i tła.',

    // Font categories
    fontCategories: {
      sansSerif: 'Bezszeryfowe',
      serif: 'Szeryfowe',
      monospace: 'Monospace',
      display: 'Wyświetlanie',
      handwriting: 'Pismo odręczne',
      pixel: 'Pikselowe',
      terminal: 'Terminalowe',
    },
    
    // Translations
    aiTranslationsDisclaimer: 'Wszystkie tłumaczenia są generowane przez AI',

    // Font names and categories
    fonts: {
      systemFont: 'Czcionka systemowa',
      systemFonts: 'Czcionki systemowe',
      serifFonts: 'Czcionki szeryfowe',
      monospaceFonts: 'Czcionki monospace',
      displayFonts: 'Czcionki wyświetlania',
      pixelFonts: 'Czcionki pikselowe',
      terminalFonts: 'Czcionki terminalowe',
      modernFonts: 'Dodatkowe czcionki nowoczesne',
      decorativeFonts: 'Dodatkowe czcionki dekoracyjne',
    },

    // Reset and export
    resetAllColors: 'Zresetuj wszystkie kolory do akcentu',
    resetAllSettings: 'Zresetuj wszystkie ustawienia',
  },

  // Lists
  lists: {
    newList: 'Nowa lista',
    listName: 'Nazwa listy',
    addLink: 'Dodaj link',
    linkName: 'Nazwa linku',
    linkUrl: 'Adres URL linku',
    editList: 'Edytuj listę',
    deleteList: 'Usuń listę',
    listIcon: 'Ikona listy',
    listColor: 'Kolor listy',
    linkColor: 'Kolor linku',
    hideIcons: 'Ukryj ikony linków',
    openInNewWindow: 'Otwórz w nowym oknie',
    copyLink: 'Kopiuj link',
    editLink: 'Edytuj link',
    deleteLink: 'Usuń link',
    title: 'Tytuł',
    url: 'Adres URL',
    addNewList: 'Dodaj listę',
    iconColor: 'Kolor ikony',
  },

  // Quick links
  fastLinks: {
    newFastLink: 'Nowy szybki link',
    fastLinkName: 'Nazwa szybkiego linku',
    fastLinkUrl: 'Adres URL',
    editFastLink: 'Edytuj szybki link',
    deleteFastLink: 'Usuń szybki link',
    fastLinkColor: 'Kolor szybkiego linku',
  },

  // Search
  search: {
    placeholder: 'Szukaj...',
    searchWith: 'Szukaj z',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Szukaj czcionek...',
    fontsNotFound: 'Nie znaleziono czcionek',
    searchInGoogle: 'Szukaj w Google...',
    searchInYandex: 'Szukaj w Yandex...',
    searchInBing: 'Szukaj w Bing...',
    searchInDuckDuckGo: 'Szukaj w DuckDuckGo...',
    searchInYahoo: 'Szukaj w Yahoo...',
    searchInBaidu: 'Szukaj w Baidu...',
    searchInStartpage: 'Szukaj w Startpage...',
    searchInSearX: 'Szukaj w SearX...',
    searchInEcosia: 'Szukaj w Ecosia...',
    searchInBrave: 'Szukaj w Brave...',
  },

  // Errors
  errors: {
    invalidUrl: 'Nieprawidłowy adres URL',
    nameRequired: 'Nazwa jest wymagana',
    urlRequired: 'Adres URL jest wymagany',
    fileUploadError: 'Błąd przesyłania pliku',
    settingsImportError: 'Błąd importowania ustawień',
    settingsExportError: 'Błąd eksportowania ustawień',
    invalidImageUrl: 'Nieprawidłowy adres URL obrazu',
    imageValidationError: 'Błąd walidacji obrazu',
    imageLoadFailed: 'Nie udało się załadować obrazu',
    imageTooSmall: 'Obraz jest za mały',
    settingsImported: 'Ustawienia zaimportowano pomyślnie! Strona zostanie ponownie załadowana, aby zastosować wszystkie zmiany.',
    settingsExported: 'Ustawienia wyeksportowano pomyślnie',
    parseError: 'Błąd parsowania:',
    invalidFileFormat: 'Błąd odczytu pliku ustawień. Upewnij się, że plik ma prawidłowy format.',
    importError: 'Błąd importowania ustawień. Sprawdź konsolę, aby uzyskać szczegóły.',
    exportError: 'Błąd eksportowania ustawień. Sprawdź konsolę, aby uzyskać szczegóły.',
    resetConfirm: 'Czy na pewno chcesz zresetować wszystkie ustawienia do wartości domyślnych? Tej operacji nie można cofnąć. Wszystkie niestandardowe ustawienia, w tym tła, listy i szybkie linki, zostaną całkowicie usunięte z pamięci lokalnej.',
    deleteListConfirm: 'Czy na pewno chcesz usunąć tę listę? Tej operacji nie można cofnąć.',
    deleteLinkConfirm: 'Czy na pewno chcesz usunąć ten link?',
    loadingError: 'Błąd ładowania',
    backgroundLoadError: 'Nie udało się załadować obrazu tła',
    criticalError: 'Wystąpił krytyczny błąd. Strona zostanie ponownie załadowana.',
    jsError: 'Błąd JavaScript',
    promiseRejection: 'Nieobsłużony błąd Promise',
  },

  // Tooltips
  tooltips: {
    settings: 'Ustawienia',
    addList: 'Dodaj listę',
    addFastLink: 'Dodaj szybki link',
    editItem: 'Edytuj',
    deleteItem: 'Usuń',
    updateItem: 'Zaktualizuj',
    resetColor: 'Zresetuj kolor',
    openAllLinks: 'Otwórz wszystkie linki w nowych kartach',
    addLink: 'Dodaj link',
    openLink: 'Otwórz link',
    copyLink: 'Kopiuj link',
    dragToReorder: 'Przeciągnij, aby zmienić kolejność',
    exportSettings: 'Eksportuj wszystkie ustawienia do pliku',
    importSettings: 'Importuj ustawienia z pliku',
    resetAllSettings: 'Zresetuj wszystkie ustawienia',
    closeSettings: 'Zamknij ustawienia',
    generateColor: 'Generuj kolor na podstawie obrazu',
    applyAccentColor: 'Zastosuj kolor akcentu do wszystkich elementów',
    addRandomPhoto: 'Dodaj losowe zdjęcie z internetu',
    deleteImage: 'Usuń obraz',
    addColor: 'Dodaj kolor',
    deleteColor: 'Usuń kolor',
    resetFilters: 'Resetuj filtry',
    expandFilters: 'Rozwiń filtry',
    collapseFilters: 'Zwiń filtry',
    stopAutoSwitch: 'Zatrzymaj automatyczne przełączanie',
    startAutoSwitch: 'Rozpocznij automatyczne przełączanie',
  },

  // Time
  time: {
    seconds: 'sek',
    minutes: 'min',
    hours: 'godz',
    days: 'dni',
    months: ['Styczeń', 'Luty', 'Marzec', 'Kwiecień', 'Maj', 'Czerwiec', 'Lipiec', 'Sierpień', 'Wrzesień', 'Październik', 'Listopad', 'Grudzień'],
    weekdays: ['Niedziela', 'Poniedziałek', 'Wtorek', 'Środa', 'Czwartek', 'Piątek', 'Sobota'],
  },

  // Units
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sek',
    minutes: 'min',
    small: 'Mały',
    medium: 'Średni',
    large: 'Duży',
  },

  // Dialogs
  dialogs: {
    newName: 'Nowa nazwa',
    newListName: 'Nazwa nowej listy',
    linkTitle: 'Tytuł linku',
    linkColor: 'Kolor linku',
    separatorColor: 'Kolor separatora',
    titleColor: 'Kolor tytułu',
  },

  // ARIA labels
  ariaLabels: {
    settings: 'Ustawienia',
    resetAllSettings: 'Zresetuj wszystkie ustawienia',
    closeSettings: 'Zamknij ustawienia',
    applyAccentColor: 'Zastosuj kolor akcentu do wszystkich elementów',
    addList: 'Dodaj listę',
    addRandomPhoto: 'Dodaj losowe zdjęcie',
    deleteImage: 'Usuń obraz',
    addColor: 'Dodaj kolor',
    deleteColor: 'Usuń kolor',
    resetFilters: 'Resetuj filtry',
    expandFilters: 'Rozwiń filtry',
    collapseFilters: 'Zwiń filtry',
  },

  // Border radius
  radius: {
    none: 'Brak',
    small: 'Mały',
    medium: 'Średni',
    large: 'Duży',
    full: 'Pełny',
  },

  // Font categories
  fonts: {
    system: 'Systemowa',
    serif: 'Szeryfowa',
    monospace: 'Monospace',
    display: 'Wyświetlanie',
    pixel: 'Pikselowa',
    terminal: 'Terminalowa',
    modern: 'Nowoczesna',
    decorative: 'Dekoracyjna',
  },
};