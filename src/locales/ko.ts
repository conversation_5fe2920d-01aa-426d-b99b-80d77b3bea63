export const ko = {
  // 일반
  common: {
    apply: '적용',
    cancel: '취소',
    save: '저장',
    delete: '삭제',
    edit: '편집',
    update: '업데이트',
    reset: '초기화',
    resetIcon: '아이콘 초기화',
    add: '추가',
    remove: '제거',
    close: '닫기',
    open: '열기',
    enable: '활성화',
    disable: '비활성화',
    show: '표시',
    hide: '숨기기',
    yes: '예',
    no: '아니오',
    loading: '로드 중...',
    error: '오류',
    success: '성공',
    warning: '경고',
    info: '정보',
    create: '생성',
    enterName: '이름 입력...',
    sidebar: '사이드바',
    sidebarDescription: '설정 및 추가 콘텐츠가 있는 사이드바',
  },

  // 설정
  settings: {
    title: '설정',
    description: '여기에 확장 프로그램 설정(테마, 모양, 옵션 등)이 있습니다.',
    basicSettings: '기본 설정',
    clockSettings: '시계 설정',
    searchSettings: '검색 엔진',
    fastLinksSettings: '빠른 링크',
    backgroundSettings: '배경',
    presetsSettings: '프리셋',
    listsSettings: '목록 설정',

    // 기본 설정
    language: '인터페이스 언어',
    exportSettings: '내보내기',
    importSettings: '가져오기',
    theme: '테마',
    accentColor: '강조 색상',
    borderRadius: '테두리 반경',
    cleanMode: '클린 모드',
    cleanModeDescription: '목록 열기 버튼을 숨깁니다.',
    colorScheme: '색 구성표',
    borderRounding: '요소 둥글게 하기',
    
    // 시계
    showClock: '시계 표시',
    showSeconds: '초 표시',
    showDate: '날짜 표시',
    clockSize: '시계 크기',
    clockColor: '시계 및 날짜 색상',
    
    // 검색
    showSearch: '검색 표시',
    searchEngine: '검색 엔진',
    searchSize: '검색 크기',
    searchBackgroundColor: '검색 배경색',
    searchBorderColor: '검색 테두리 색상',
    searchTextColor: '검색 텍스트 색상',
    searchBackdropBlur: '검색 배경 흐림',
    showSearchEngine: '검색 엔진 표시',
    backgroundBlur: '배경 흐림',
    
    // 빠른 링크
    showFastLinks: '빠른 링크 표시',
    fastLinksColumns: '열 개수',
    fastLinksSize: '빠른 링크 크기',
    fastLinksBackdropBlur: '빠른 링크 배경 흐림',
    addFastLink: '빠른 링크 추가',
    textColor: '제목 텍스트 색상',
    backdropColor: '배경 색상',
    iconBackgroundColor: '아이콘 배경색',
    colors: '색상',
    display: '표시',
    hideIcons: '아이콘 숨기기',
    hideText: '텍스트 숨기기',
    backgroundEffects: '아이콘 배경 효과',

    // 목록
    showLists: '링크 목록 표시',
    listsColumns: '열 개수',
    listsBackgroundColor: '목록 배경색',
    listsBackdropBlur: '목록 배경 흐림',
    addNewList: '새 목록 추가',
    listBackground: '목록 배경',
    backgroundColor: '배경색',
    borderColor: '테두리 색상',
    borderThickness: '테두리 두께',
    hideBorder: '테두리 숨기기',
    hideBackground: '배경 숨기기',
    separator: '구분선',
    separatorColor: '구분선 색상',
    separatorThickness: '구분선 두께',
    hideSeparator: '구분선 숨기기',
    colorsAndIcons: '색상 및 아이콘',
    titleColor: '제목 색상',
    linkColor: '링크 색상',
    hideLinkIcons: '링크 아이콘 숨기기',
    listGrid: '목록 그리드',
    columnsCount: '열 개수',
    listManagement: '목록 관리',
    
    // 배경
    backgroundType: '배경 유형',
    solidColor: '단색',
    gradient: '그라데이션',
    image: '이미지',
    brightness: '밝기',
    contrast: '대비',
    saturation: '채도',
    blur: '흐림',
    hueRotate: '색조 회전',
    sepia: '세피아',
    grayscale: '흑백',
    invert: '반전',
    shadowOverlay: '그림자 오버레이',
    parallaxEffect: '시차 효과',
    autoSwitch: '자동 전환',
    switchInterval: '전환 간격',
    addImages: '이미지 추가',
    uploadImages: '이미지 업로드',
    addImage: '이미지 추가',
    checking: '확인 중...',
    addRandomPhoto: '인터넷에서 임의 사진 추가',
    parallaxDescription: '배경 이미지는 마우스 움직임을 따릅니다.',
    shadowBottom: '하단 그림자',
    shadowDescription: '목록 가시성 향상을 위한 그라데이션 그림자',
    intensity: '강도:',
    height: '높이:',
    gallery: '갤러리',
    startAutoSwitch: '자동 전환 시작',
    stopAutoSwitch: '자동 전환 중지',
    onLoad: '로드 시',
    daily: '매일',
    deleteImage: '이미지 삭제',
    
    // 그라데이션
    gradientType: '그라데이션 유형',
    addColor: '색상 추가',
    deleteColor: '색상 삭제',
    direction: '방향',
    position: '위치',
    customCSS: '사용자 지정 CSS 그라데이션 (선택 사항)',
    customCSSDescription: '수동 설정 대신 적용할 CSS 그라데이션 문자열을 입력합니다.',
    backgroundFilters: '배경 필터',
    resetFilters: '필터 초기화',
    expandFilters: '필터 확장',
    collapseFilters: '필터 축소',
    color: '색상',
    filters: '배경 필터',
    font: '글꼴',
    right: '오른쪽',
    left: '왼쪽',
    bottom: '아래',
    top: '위',
    bottomRight: '오른쪽 아래',
    bottomLeft: '왼쪽 아래',
    topRight: '오른쪽 위',
    topLeft: '왼쪽 위',
    center: '중앙',
    gradientDirection: '그라데이션 방향',
    gradientColor1: '색상 1',
    gradientColor2: '색상 2',
    gradientColor3: '색상 3',
    gradientColor4: '색상 4',
    gradientColor5: '색상 5',
    gradientColor6: '색상 6',
    customGradient: '사용자 지정 그라데이션',
    customGradientCSS: '사용자 지정 CSS',
    gradientPreview: '미리보기',
    linear: '선형',
    radial: '방사형',
    conic: '원추형',
    toRight: '오른쪽으로',
    toLeft: '왼쪽으로',
    toBottom: '아래로',
    toTop: '위로',
    toBottomRight: '오른쪽 아래로',
    toBottomLeft: '왼쪽 아래로',
    toTopRight: '오른쪽 위로',
    toTopLeft: '왼쪽 위로',
    circle: '원',
    ellipse: '타원',
    fromCenter: '중앙에서',
    
    // 이미지 필터
    imageFilters: '이미지 필터',
    
    // 프리셋
    presets: '프리셋',
    createPreset: '프리셋 생성',
    presetName: '프리셋 이름',
    noPresets: '생성된 프리셋이 없습니다. 설정을 빠르게 전환하려면 첫 번째 프리셋을 생성하세요.',
    renamePreset: '프리셋 이름 바꾸기',
    updatePreset: '현재 설정으로 프리셋 업데이트',
    deletePreset: '프리셋 삭제',
    createNewPreset: '새 프리셋 생성',
    presetDescription: '프리셋은 현재 색상, 글꼴 및 배경 설정을 저장합니다.',

    // 글꼴 카테고리
    fontCategories: {
      sansSerif: '산세리프',
      serif: '세리프',
      monospace: '모노스페이스',
      display: '디스플레이',
      handwriting: '손글씨',
      pixel: '픽셀',
      terminal: '터미널',
    },
    
    // 번역
    aiTranslationsDisclaimer: '모든 번역은 AI에 의해 생성되었습니다.',

    // 글꼴 이름 및 카테고리
    fonts: {
      systemFont: '시스템 글꼴',
      systemFonts: '시스템 글꼴',
      serifFonts: '세리프 글꼴',
      monospaceFonts: '모노스페이스 글꼴',
      displayFonts: '디스플레이 글꼴',
      pixelFonts: '픽셀 글꼴',
      terminalFonts: '터미널 글꼴',
      modernFonts: '추가 현대 글꼴',
      decorativeFonts: '추가 장식 글꼴',
    },

    // 초기화 및 내보내기
    resetAllColors: '모든 색상을 강조 색상으로 초기화',
    resetAllSettings: '모든 설정 초기화',
  },

  // 목록
  lists: {
    newList: '새 목록',
    listName: '목록 이름',
    addLink: '링크 추가',
    linkName: '링크 이름',
    linkUrl: '링크 URL',
    editList: '목록 편집',
    deleteList: '목록 삭제',
    listIcon: '목록 아이콘',
    listColor: '목록 색상',
    linkColor: '링크 색상',
    hideIcons: '링크 아이콘 숨기기',
    openInNewWindow: '새 창에서 열기',
    copyLink: '링크 복사',
    editLink: '링크 편집',
    deleteLink: '링크 삭제',
    title: '제목',
    url: 'URL',
    addNewList: '목록 추가',
    iconColor: '아이콘 색상',
  },

  // 빠른 링크
  fastLinks: {
    newFastLink: '새 빠른 링크',
    fastLinkName: '빠른 링크 이름',
    fastLinkUrl: 'URL',
    editFastLink: '빠른 링크 편집',
    deleteFastLink: '빠른 링크 삭제',
    fastLinkColor: '빠른 링크 색상',
  },

  // 검색
  search: {
    placeholder: '검색...',
    searchWith: '다음으로 검색',
    google: '구글',
    yandex: '얀덱스',
    bing: '빙',
    duckduckgo: 'DuckDuckGo',
    searchFonts: '글꼴 검색...',
    fontsNotFound: '글꼴을 찾을 수 없습니다.',
    searchInGoogle: '구글에서 검색...',
    searchInYandex: '얀덱스에서 검색...',
    searchInBing: '빙에서 검색...',
    searchInDuckDuckGo: 'DuckDuckGo에서 검색...',
    searchInYahoo: '야후에서 검색...',
    searchInBaidu: '바이두에서 검색...',
    searchInStartpage: 'Startpage에서 검색...',
    searchInSearX: 'SearX에서 검색...',
    searchInEcosia: 'Ecosia에서 검색...',
    searchInBrave: 'Brave에서 검색...',
  },

  // 오류
  errors: {
    invalidUrl: '유효하지 않은 URL',
    nameRequired: '이름은 필수입니다.',
    urlRequired: 'URL은 필수입니다.',
    fileUploadError: '파일 업로드 오류',
    settingsImportError: '설정 가져오기 오류',
    settingsExportError: '설정 내보내기 오류',
    invalidImageUrl: '유효하지 않은 이미지 URL',
    imageValidationError: '이미지 유효성 검사 오류',
    imageLoadFailed: '이미지 로드 실패',
    imageTooSmall: '이미지가 너무 작습니다.',
    settingsImported: '설정을 성공적으로 가져왔습니다! 모든 변경 사항을 적용하기 위해 페이지가 다시 로드됩니다.',
    settingsExported: '설정을 성공적으로 내보냈습니다.',
    parseError: '구문 분석 오류:',
    invalidFileFormat: '설정 파일을 읽는 데 오류가 발생했습니다. 파일 형식이 올바른지 확인하십시오.',
    importError: '설정 가져오기 중 오류가 발생했습니다. 자세한 내용은 콘솔을 확인하십시오.',
    exportError: '설정 내보내기 중 오류가 발생했습니다. 자세한 내용은 콘솔을 확인하십시오.',
    resetConfirm: '모든 설정을 기본값으로 초기화하시겠습니까? 이 작업은 되돌릴 수 없습니다. 배경, 목록 및 빠른 링크를 포함한 모든 사용자 지정 설정이 로컬 스토리지에서 완전히 제거됩니다.',
    deleteListConfirm: '이 목록을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.',
    deleteLinkConfirm: '이 링크를 삭제하시겠습니까?',
    loadingError: '로드 오류',
    backgroundLoadError: '배경 이미지 로드 실패',
    criticalError: '치명적인 오류가 발생했습니다. 페이지가 다시 로드됩니다.',
    jsError: 'JavaScript 오류',
    promiseRejection: '처리되지 않은 Promise 오류',
  },

  // 툴팁
  tooltips: {
    settings: '설정',
    addList: '목록 추가',
    addFastLink: '빠른 링크 추가',
    editItem: '편집',
    deleteItem: '삭제',
    updateItem: '업데이트',
    resetColor: '색상 초기화',
    openAllLinks: '모든 링크를 새 탭에서 열기',
    addLink: '링크 추가',
    openLink: '링크 열기',
    copyLink: '링크 복사',
    dragToReorder: '끌어서 재정렬',
    exportSettings: '모든 설정을 파일로 내보내기',
    importSettings: '파일에서 설정 가져오기',
    resetAllSettings: '모든 설정 초기화',
    closeSettings: '설정 닫기',
    generateColor: '이미지를 기반으로 색상 생성',
    applyAccentColor: '모든 요소에 강조 색상 적용',
    addRandomPhoto: '인터넷에서 임의 사진 추가',
    deleteImage: '이미지 삭제',
    addColor: '색상 추가',
    deleteColor: '색상 삭제',
    resetFilters: '필터 초기화',
    expandFilters: '필터 확장',
    collapseFilters: '필터 축소',
    stopAutoSwitch: '자동 전환 중지',
    startAutoSwitch: '자동 전환 시작',
  },

  // 시간
  time: {
    seconds: '초',
    minutes: '분',
    hours: '시간',
    days: '일',
    months: ['1월', '2월', '3월', '4월', '5월', '6월', '7월', '8월', '9월', '10월', '11월', '12월'],
    weekdays: ['일요일', '월요일', '화요일', '수요일', '목요일', '금요일', '토요일'],
  },

  // 단위
  units: {
    pixels: 'px',
    percent: '%',
    seconds: '초',
    minutes: '분',
    small: '작음',
    medium: '중간',
    large: '큼',
  },

  // 대화 상자
  dialogs: {
    newName: '새 이름',
    newListName: '새 목록 이름',
    linkTitle: '링크 제목',
    linkColor: '링크 색상',
    separatorColor: '구분선 색상',
    titleColor: '제목 색상',
  },

  // ARIA 레이블
  ariaLabels: {
    settings: '설정',
    resetAllSettings: '모든 설정 초기화',
    closeSettings: '설정 닫기',
    applyAccentColor: '모든 요소에 강조 색상 적용',
    addList: '목록 추가',
    addRandomPhoto: '임의 사진 추가',
    deleteImage: '이미지 삭제',
    addColor: '색상 추가',
    deleteColor: '색상 삭제',
    resetFilters: '필터 초기화',
    expandFilters: '필터 확장',
    collapseFilters: '필터 축소',
  },

  // 테두리 반경
  radius: {
    none: '없음',
    small: '작게',
    medium: '중간',
    large: '크게',
    full: '전체',
  },

  // 글꼴 카테고리
  fonts: {
    system: '시스템',
    serif: '세리프',
    monospace: '모노스페이스',
    display: '디스플레이',
    pixel: '픽셀',
    terminal: '터미널',
    modern: '현대',
    decorative: '장식',
  },
};