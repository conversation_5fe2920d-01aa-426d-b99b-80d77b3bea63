// Deutsches Wörterbuch für Übersetzungen
export const de = {
  // Allgemein
  common: {
    apply: '<PERSON><PERSON><PERSON>',
    cancel: 'Abbrechen',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON>',
    edit: 'Bearbei<PERSON>',
    update: 'Aktualisieren',
    reset: '<PERSON><PERSON><PERSON>set<PERSON>',
    resetIcon: 'Icon zurücksetzen',
    add: 'Hinz<PERSON>ü<PERSON>',
    remove: 'Ent<PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON>ße<PERSON>',
    open: '<PERSON><PERSON><PERSON>',
    enable: 'Aktivieren',
    disable: 'Deaktivieren',
    show: 'Anzeigen',
    hide: 'Ausblenden',
    yes: 'Ja',
    no: 'Nein',
    loading: 'Lädt...',
    error: 'Fehler',
    success: 'Erfolgreich',
    warning: 'Warnung',
    info: 'Information',
    create: '<PERSON><PERSON>elle<PERSON>',
    enterName: 'Name eingeben...',
    sidebar: 'Seitenleiste',
    sidebarDescription: 'Seitenleiste mit Einstellungen und zusätzlichen Inhalten',
  },

  // Einstellungen
  settings: {
    title: 'Einstellungen',
    description: 'Hier finden Sie die Erweiterungseinstellungen (Thema, Aussehen, Optionen usw.)',
    basicSettings: 'Grundeinstellungen',
    clockSettings: 'Uhr-Einstellungen',
    searchSettings: 'Suchmaschine',
    fastLinksSettings: 'Schnelllinks',
    backgroundSettings: 'Hintergrund',
    presetsSettings: 'Voreinstellungen',
    listsSettings: 'Listen-Einstellungen',

    // Grundeinstellungen
    language: 'Oberflächensprache',
    exportSettings: 'Exportieren',
    importSettings: 'Importieren',
    theme: 'Thema',
    accentColor: 'Akzentfarbe',
    borderRadius: 'Rahmenradius',
    cleanMode: 'Sauberer Modus',
    cleanModeDescription: 'Blendet die Schaltflächen zum Öffnen von Listen aus',
    colorScheme: 'Farbschema',
    borderRounding: 'Abgerundete Elemente',
    
    // Uhr
    showClock: 'Uhr anzeigen',
    showSeconds: 'Sekunden anzeigen',
    showDate: 'Datum anzeigen',
    clockSize: 'Uhrgröße',
    clockColor: 'Farbe von Uhr und Datum',

    // Suche
    showSearch: 'Suchleiste anzeigen',
    searchEngine: 'Suchmaschine',
    searchSize: 'Größe der Suchleiste',
    searchBackgroundColor: 'Hintergrundfarbe der Suchleiste',
    searchBorderColor: 'Rahmenfarbe der Suchleiste',
    searchTextColor: 'Textfarbe der Suchleiste',
    searchBackdropBlur: 'Hintergrundunschärfe der Suchleiste',
    showSearchEngine: 'Suchmaschine anzeigen',
    backgroundBlur: 'Hintergrundunschärfe',
    
    // Schnelllinks
    showFastLinks: 'Schnelllinks anzeigen',
    fastLinksColumns: 'Anzahl der Spalten',
    fastLinksSize: 'Größe der Schnelllinks',
    fastLinksBackdropBlur: 'Hintergrundunschärfe für Schnelllinks',
    addFastLink: 'Schnelllink hinzufügen',
    textColor: 'Textfarbe der Titel',
    backdropColor: 'Hintergrundfarbe',
    iconBackgroundColor: 'Hintergrundfarbe der Icons',
    colors: 'Farben',
    display: 'Anzeige',
    hideIcons: 'Icons ausblenden',
    hideText: 'Text ausblenden',
    backgroundEffects: 'Hintergrundeffekte für Icons',

    // Listen
    showLists: 'Linkslisten anzeigen',
    listsColumns: 'Anzahl der Spalten',
    listsBackgroundColor: 'Hintergrundfarbe der Listen',
    listsBackdropBlur: 'Hintergrundunschärfe für Listen',
    addNewList: 'Neue Liste hinzufügen',
    listBackground: 'Listen-Hintergrund',
    backgroundColor: 'Hintergrundfarbe',
    borderColor: 'Rahmenfarbe',
    borderThickness: 'Rahmenstärke',
    hideBorder: 'Rahmen ausblenden',
    hideBackground: 'Hintergrund ausblenden',
    separator: 'Trennlinie',
    separatorColor: 'Farbe der Trennlinie',
    separatorThickness: 'Stärke der Trennlinie',
    hideSeparator: 'Trennlinie ausblenden',
    colorsAndIcons: 'Farben und Icons',
    titleColor: 'Titelfarbe',
    linkColor: 'Linkfarbe',
    hideLinkIcons: 'Link-Icons ausblenden',
    listGrid: 'Listenraster',
    columnsCount: 'Spaltenanzahl',
    listManagement: 'Listenverwaltung',
    
    // Hintergrund
    backgroundType: 'Hintergrundtyp',
    solidColor: 'Einfarbig',
    gradient: 'Verlauf',
    image: 'Bild',
    addImage: 'Bild hinzufügen',
    checking: 'Überprüfung...',
    addRandomPhoto: 'Zufälliges Foto aus dem Internet hinzufügen',
    parallaxEffect: 'Parallax-Effekt',
    parallaxDescription: 'Hintergrundbild folgt der Mausbewegung',
    shadowBottom: 'Untere Schattierung',
    shadowDescription: 'Verlaufsschattierung für bessere Sichtbarkeit der Listen',
    intensity: 'Intensität:',
    height: 'Höhe:',
    linear: 'Linear',
    radial: 'Radial',
    gallery: 'Galerie',
    startAutoSwitch: 'Automatisches Wechseln starten',
    stopAutoSwitch: 'Automatisches Wechseln stoppen',
    onLoad: 'Beim Laden',
    daily: 'Täglich',
    deleteImage: 'Bild löschen',
    gradientType: 'Verlaufstyp',
    addColor: 'Farbe hinzufügen',
    deleteColor: 'Farbe löschen',
    direction: 'Richtung',
    position: 'Position',
    customCSS: 'CSS-Verlauf (optional)',
    customCSSDescription: 'CSS-Verlaufsstring zur Verwendung anstelle manueller Einstellungen',
    backgroundFilters: 'Hintergrundfilter',
    resetFilters: 'Filter zurücksetzen',
    expandFilters: 'Filter erweitern',
    collapseFilters: 'Filter einklappen',
    blur: 'Unschärfe',
    brightness: 'Helligkeit',
    contrast: 'Kontrast',
    saturation: 'Sättigung',
    hueRotate: 'Farbtonrotation',
    sepia: 'Sepia',
    grayscale: 'Graustufen',
    invert: 'Invertieren',
    color: 'Farbe',

    shadowOverlay: 'Schatten-Overlay',
    autoSwitch: 'Automatisches Wechseln',
    switchInterval: 'Wechselintervall',
    addImages: 'Bilder hinzufügen',
    uploadImages: 'Bilder hochladen',
    filters: 'Hintergrundfilter',
    font: 'Schriftart',
    right: 'Rechts',
    left: 'Links',
    bottom: 'Unten',
    top: 'Oben',
    bottomRight: 'Unten rechts',
    bottomLeft: 'Unten links',
    topRight: 'Oben rechts',
    topLeft: 'Oben links',
    center: 'Mitte',

    // Voreinstellungen
    presets: 'Voreinstellungen',
    createPreset: 'Voreinstellung erstellen',
    presetName: 'Name der Voreinstellung',
    noPresets: 'Keine Voreinstellungen vorhanden. Erstellen Sie eine Voreinstellung für schnelles Umschalten zwischen Konfigurationen.',
    renamePreset: 'Voreinstellung umbenennen',
    updatePreset: 'Voreinstellung mit aktuellen Einstellungen aktualisieren',
    deletePreset: 'Voreinstellung löschen',
    createNewPreset: 'Neue Voreinstellung erstellen',
    presetDescription: 'Speichert aktuelle Farb-, Schrift- und Hintergrundeinstellungen.',

    // Schriftkategorien
    fontCategories: {
      sansSerif: 'Serifenlos',
      serif: 'Serif',
      monospace: 'Monospace',
      display: 'Dekorativ',
      handwriting: 'Handschriftlich',
      pixel: 'Pixel',
      terminal: 'Terminal'
    },

    // Übersetzungen
    aiTranslationsDisclaimer: 'Alle Übersetzungen wurden mit KI erstellt',

    // Schriftnamen und Kategorien
    fonts: {
      systemFont: 'Systemschriftart',
      systemFonts: 'Systemschriftarten',
      serifFonts: 'Serif-Schriftarten',
      monospaceFonts: 'Monospace-Schriftarten',
      displayFonts: 'Dekorative Schriftarten',
      pixelFonts: 'Pixel-Schriftarten',
      terminalFonts: 'Terminal-Schriftarten',
      modernFonts: 'Zusätzliche moderne Schriftarten',
      decorativeFonts: 'Zusätzliche dekorative Schriftarten',
    },

    // Zurücksetzen und Export
    resetAllColors: 'Alle Farben auf Akzentfarbe zurücksetzen',
    resetAllSettings: 'Alle Einstellungen zurücksetzen',
  },

  // Listen
  lists: {
    newList: 'Neue Liste',
    listName: 'Listenname',
    addLink: 'Link hinzufügen',
    linkName: 'Linkname',
    linkUrl: 'Link-URL',
    editList: 'Liste bearbeiten',
    deleteList: 'Liste löschen',
    listIcon: 'Listen-Icon',
    listColor: 'Listenfarbe',
    linkColor: 'Linkfarbe',
    hideIcons: 'Link-Icons ausblenden',
    openInNewWindow: 'In neuem Fenster öffnen',
    copyLink: 'Link kopieren',
    editLink: 'Link bearbeiten',
    deleteLink: 'Link löschen',
    title: 'Titel',
    url: 'URL',
    addNewList: 'Liste hinzufügen',
    iconColor: 'Icon-Farbe',
  },

  // Dialoge und Formulare
  dialogs: {
    newName: 'Neuer Name',
    newListName: 'Neuer Listenname',
    linkTitle: 'Link-Titel',
    linkColor: 'Linkfarbe',
    separatorColor: 'Trennlinienfarbe',
    titleColor: 'Titelfarbe',
  },

  // Schnelllinks
  fastLinks: {
    newFastLink: 'Neuer Schnelllink',
    fastLinkName: 'Schnelllink-Name',
    fastLinkUrl: 'URL',
    editFastLink: 'Schnelllink bearbeiten',
    deleteFastLink: 'Schnelllink löschen',
    fastLinkColor: 'Schnelllink-Farbe',
  },

  // Suche
  search: {
    placeholder: 'Suchen...',
    searchWith: 'Suchen mit',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Schriftarten suchen...',
    fontsNotFound: 'Keine Schriftarten gefunden',
    searchInGoogle: 'Mit Google suchen...',
    searchInYandex: 'Mit Yandex suchen...',
    searchInBing: 'Mit Bing suchen...',
    searchInDuckDuckGo: 'Mit DuckDuckGo suchen...',
    searchInYahoo: 'Mit Yahoo suchen...',
    searchInBaidu: 'Mit Baidu suchen...',
    searchInStartpage: 'Mit Startpage suchen...',
    searchInSearX: 'Mit SearX suchen...',
    searchInEcosia: 'Mit Ecosia suchen...',
    searchInBrave: 'Mit Brave suchen...',
  },

  // Fehler und Benachrichtigungen
  errors: {
    invalidUrl: 'Ungültige URL',
    nameRequired: 'Name ist erforderlich',
    urlRequired: 'URL ist erforderlich',
    fileUploadError: 'Fehler beim Hochladen der Datei',
    settingsImportError: 'Fehler beim Import der Einstellungen',
    settingsExportError: 'Fehler beim Export der Einstellungen',
    invalidImageUrl: 'Ungültige Bild-URL',
    imageValidationError: 'Fehler bei der Bildüberprüfung',
    imageLoadFailed: 'Bild konnte nicht geladen werden',
    imageTooSmall: 'Bild ist zu klein',
    settingsImported: 'Einstellungen erfolgreich importiert! Seite wird neu geladen, um Änderungen zu übernehmen.',
    settingsExported: 'Einstellungen erfolgreich exportiert',
    parseError: 'Fehler beim Parsen der Datei:',
    invalidFileFormat: 'Fehler beim Lesen der Einstellungsdatei. Stellen Sie sicher, dass das Dateiformat korrekt ist.',
    importError: 'Fehler beim Import der Einstellungen. Details siehe Konsole.',
    exportError: 'Fehler beim Export der Einstellungen. Details siehe Konsole.',
    resetConfirm: 'Sind Sie sicher, dass Sie alle Einstellungen auf Standardwerte zurücksetzen möchten? Diese Aktion kann nicht rückgängig gemacht werden. Alle benutzerdefinierten Einstellungen, inklusive Hintergründe, Listen und Schnelllinks, werden aus dem localStorage gelöscht.',
    deleteListConfirm: 'Sind Sie sicher, dass Sie diese Liste löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    deleteLinkConfirm: 'Sind Sie sicher, dass Sie diesen Link löschen möchten?',
    loadingError: 'Ladefehler',
    backgroundLoadError: 'Hintergrundbild konnte nicht geladen werden',
    criticalError: 'Ein kritischer Fehler ist aufgetreten. Die Seite wird neu geladen.',
    jsError: 'JavaScript-Fehler',
    promiseRejection: 'Unbehandelter Promise-Fehler',
  },

  // Tooltips
  tooltips: {
    settings: 'Einstellungen',
    addList: 'Liste hinzufügen',
    addFastLink: 'Schnelllink hinzufügen',
    editItem: 'Bearbeiten',
    deleteItem: 'Löschen',
    updateItem: 'Aktualisieren',
    resetColor: 'Farbe zurücksetzen',
    openAllLinks: 'Alle Links in neuen Tabs öffnen',
    addLink: 'Link hinzufügen',
    openLink: 'Link öffnen',
    copyLink: 'Link kopieren',
    dragToReorder: 'Zum Neuordnen ziehen',
    exportSettings: 'Alle Einstellungen in Datei exportieren',
    importSettings: 'Einstellungen aus Datei importieren',
    resetAllSettings: 'Alle Einstellungen zurücksetzen',
    closeSettings: 'Einstellungen schließen',
    generateColor: 'Farbe basierend auf Bild generieren',
    applyAccentColor: 'Akzentfarbe auf alle Elemente anwenden',
    addRandomPhoto: 'Zufälliges Foto aus dem Internet hinzufügen',
    deleteImage: 'Bild löschen',
    addColor: 'Farbe hinzufügen',
    deleteColor: 'Farbe löschen',
    resetFilters: 'Filter zurücksetzen',
    expandFilters: 'Filter erweitern',
    collapseFilters: 'Filter einklappen',
    stopAutoSwitch: 'Automatisches Wechseln stoppen',
    startAutoSwitch: 'Automatisches Wechseln starten',
  },

  // Aria-Labels
  ariaLabels: {
    settings: 'Einstellungen',
    resetAllSettings: 'Alle Einstellungen zurücksetzen',
    closeSettings: 'Einstellungen schließen',
    applyAccentColor: 'Akzentfarbe auf alle Elemente anwenden',
    addList: 'Liste hinzufügen',
    addRandomPhoto: 'Zufälliges Foto hinzufügen',
    deleteImage: 'Bild löschen',
    addColor: 'Farbe hinzufügen',
    deleteColor: 'Farbe löschen',
    resetFilters: 'Filter zurücksetzen',
    expandFilters: 'Filter erweitern',
    collapseFilters: 'Filter einklappen',
  },

  // Rundungsradius
  radius: {
    none: 'Keine Rundung',
    small: 'Klein',
    medium: 'Mittel',
    large: 'Groß',
    full: 'Voll',
  },

  // Zeit und Datum
  time: {
    seconds: 'Sek',
    minutes: 'Min',
    hours: 'Std',
    days: 'Tg',
    months: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
    weekdays: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'],
  },

  // Größen und Einheiten
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'Sek',
    minutes: 'Min',
    small: 'Klein',
    medium: 'Mittel',
    large: 'Groß',
  },
};
