export const uk = {
  // Загальне
  common: {
    apply: 'Застосувати',
    cancel: 'Скасувати',
    save: 'Зберегти',
    delete: 'Видалити',
    edit: 'Редагувати',
    update: 'Оновити',
    reset: 'Скинути',
    resetIcon: 'Скинути іконку',
    add: 'Додати',
    remove: 'Видалити',
    close: 'Закрити',
    open: 'Відкрити',
    enable: 'Увімкнути',
    disable: 'Вимкнути',
    show: 'Показати',
    hide: 'Приховати',
    yes: 'Так',
    no: 'Ні',
    loading: 'Завантаження...',
    error: 'Помилка',
    success: 'Успіх',
    warning: 'Попередження',
    info: 'Інформація',
    create: 'Створити',
    enterName: 'Введіть ім\'я...',
    sidebar: 'Бічна панель',
    sidebarDescription: 'Бічна панель з налаштуваннями та додатковим вмістом',
  },

  // Налаштування
  settings: {
    title: 'Налаштування',
    description: 'Тут будуть налаштування розширення (тема, вигляд, опції тощо)',
    basicSettings: 'Основні налаштування',
    clockSettings: 'Налаштування годинника',
    searchSettings: 'Пошукова система',
    fastLinksSettings: 'Швидкі посилання',
    backgroundSettings: 'Фон',
    presetsSettings: 'Пресети',
    listsSettings: 'Налаштування списків',

    // Основні налаштування
    language: 'Мова інтерфейсу',
    exportSettings: 'Експорт',
    importSettings: 'Імпорт',
    theme: 'Тема',
    accentColor: 'Колір акценту',
    borderRadius: 'Радіус заокруглення',
    cleanMode: 'Чистий режим',
    cleanModeDescription: 'Приховує кнопки відкриття списку',
    colorScheme: 'Колірна схема',
    borderRounding: 'Заокруглення елементів',
    
    // Годинник
    showClock: 'Показати годинник',
    showSeconds: 'Показати секунди',
    showDate: 'Показати дату',
    clockSize: 'Розмір годинника',
    clockColor: 'Колір годинника та дати',
    
    // Пошук
    showSearch: 'Показати пошук',
    searchEngine: 'Пошукова система',
    searchSize: 'Розмір пошуку',
    searchBackgroundColor: 'Колір фону пошуку',
    searchBorderColor: 'Колір рамки пошуку',
    searchTextColor: 'Колір тексту пошуку',
    searchBackdropBlur: 'Розмиття фону пошуку',
    showSearchEngine: 'Показати пошукову систему',
    backgroundBlur: 'Розмиття фону',
    
    // Швидкі посилання
    showFastLinks: 'Показати швидкі посилання',
    fastLinksColumns: 'Кількість колонок',
    fastLinksSize: 'Розмір швидкого посилання',
    fastLinksBackdropBlur: 'Розмиття фону швидкого посилання',
    addFastLink: 'Додати швидке посилання',
    textColor: 'Колір тексту заголовка',
    backdropColor: 'Колір підкладки',
    iconBackgroundColor: 'Колір фону іконки',
    colors: 'Кольори',
    display: 'Відображення',
    hideIcons: 'Приховати іконки',
    hideText: 'Приховати текст',
    backgroundEffects: 'Ефекти фону іконки',

    // Списки
    showLists: 'Показати списки посилань',
    listsColumns: 'Кількість колонок',
    listsBackgroundColor: 'Колір фону списків',
    listsBackdropBlur: 'Розмиття фону списків',
    addNewList: 'Додати новий список',
    listBackground: 'Фон списку',
    backgroundColor: 'Колір фону',
    borderColor: 'Колір рамки',
    borderThickness: 'Товщина рамки',
    hideBorder: 'Приховати рамку',
    hideBackground: 'Приховати фон',
    separator: 'Роздільник',
    separatorColor: 'Колір роздільника',
    separatorThickness: 'Товщина роздільника',
    hideSeparator: 'Приховати роздільник',
    colorsAndIcons: 'Кольори та іконки',
    titleColor: 'Колір заголовка',
    linkColor: 'Колір посилання',
    hideLinkIcons: 'Приховати іконки посилань',
    listGrid: 'Сітка списку',
    columnsCount: 'Кількість колонок',
    listManagement: 'Керування списками',
    
    // Фон
    backgroundType: 'Тип фону',
    solidColor: 'Суцільний колір',
    gradient: 'Градієнт',
    image: 'Зображення',
    brightness: 'Яскравість',
    contrast: 'Контраст',
    saturation: 'Насиченість',
    blur: 'Розмиття',
    hueRotate: 'Обертання відтінку',
    sepia: 'Сепія',
    grayscale: 'Відтінки сірого',
    invert: 'Інвертувати',
    shadowOverlay: 'Накладання тіні',
    parallaxEffect: 'Ефект паралаксу',
    autoSwitch: 'Автоматичне перемикання',
    switchInterval: 'Інтервал перемикання',
    addImages: 'Додати зображення',
    uploadImages: 'Завантажити зображення',
    addImage: 'Додати зображення',
    checking: 'Перевірка...',
    addRandomPhoto: 'Додати випадкове фото з інтернету',
    parallaxDescription: 'Фонове зображення буде слідувати за рухом миші',
    shadowBottom: 'Нижня тінь',
    shadowDescription: 'Градієнтна тінь для кращої видимості списку',
    intensity: 'Інтенсивність:',
    height: 'Висота:',
    gallery: 'Галерея',
    startAutoSwitch: 'Запустити автоматичне перемикання',
    stopAutoSwitch: 'Зупинити автоматичне перемикання',
    onLoad: 'При завантаженні',
    daily: 'Щоденно',
    deleteImage: 'Видалити зображення',
    
    // Градієнти
    gradientType: 'Тип градієнта',
    addColor: 'Додати колір',
    deleteColor: 'Видалити колір',
    direction: 'Напрямок',
    position: 'Позиція',
    customCSS: 'CSS градієнт (опціонально)',
    customCSSDescription: 'Введіть рядок CSS градієнта для застосування замість ручного налаштування',
    backgroundFilters: 'Фонові фільтри',
    resetFilters: 'Скинути фільтри',
    expandFilters: 'Розгорнути фільтри',
    collapseFilters: 'Згорнути фільтри',
    color: 'Колір',
    filters: 'Фонові фільтри',
    font: 'Шрифт',
    right: 'Праворуч',
    left: 'Ліворуч',
    bottom: 'Знизу',
    top: 'Зверху',
    bottomRight: 'Внизу праворуч',
    bottomLeft: 'Внизу ліворуч',
    topRight: 'Вгорі праворуч',
    topLeft: 'Вгорі ліворуч',
    center: 'Центр',
    gradientDirection: 'Напрямок градієнта',
    gradientColor1: 'Колір 1',
    gradientColor2: 'Колір 2',
    gradientColor3: 'Колір 3',
    gradientColor4: 'Колір 4',
    gradientColor5: 'Колір 5',
    gradientColor6: 'Колір 6',
    customGradient: 'Власний градієнт',
    customGradientCSS: 'Власний CSS',
    gradientPreview: 'Попередній перегляд',
    linear: 'Лінійний',
    radial: 'Радіальний',
    conic: 'Конічний',
    toRight: 'Праворуч',
    toLeft: 'Ліворуч',
    toBottom: 'Вниз',
    toTop: 'Вгору',
    toBottomRight: 'Вниз праворуч',
    toBottomLeft: 'Вниз ліворуч',
    toTopRight: 'Вгору праворуч',
    toTopLeft: 'Вгору ліворуч',
    circle: 'Коло',
    ellipse: 'Еліпс',
    fromCenter: 'З центру',
    
    // Фільтри зображень
    imageFilters: 'Фільтри зображень',
    
    // Пресети
    presets: 'Пресети',
    createPreset: 'Створити пресет',
    presetName: 'Назва пресету',
    noPresets: 'Пресети не створено. Створіть свій перший пресет для швидкого перемикання між налаштуваннями.',
    renamePreset: 'Перейменувати пресет',
    updatePreset: 'Оновити пресет поточними налаштуваннями',
    deletePreset: 'Видалити пресет',
    createNewPreset: 'Створити новий пресет',
    presetDescription: 'Пресет збереже поточні налаштування кольору, шрифту та фону.',

    // Категорії шрифтів
    fontCategories: {
      sansSerif: 'Без зарубок',
      serif: 'Із зарубками',
      monospace: 'Моноширинний',
      display: 'Дисплейний',
      handwriting: 'Рукописний',
      pixel: 'Піксельний',
      terminal: 'Термінальний',
    },
    
    // Переклади
    aiTranslationsDisclaimer: 'Усі переклади згенеровані ШІ',

    // Назви та категорії шрифтів
    fonts: {
      systemFont: 'Системний шрифт',
      systemFonts: 'Системні шрифти',
      serifFonts: 'Шрифти із зарубками',
      monospaceFonts: 'Моноширинні шрифти',
      displayFonts: 'Дисплейні шрифти',
      pixelFonts: 'Піксельні шрифти',
      terminalFonts: 'Термінальні шрифти',
      modernFonts: 'Додаткові сучасні шрифти',
      decorativeFonts: 'Додаткові декоративні шрифти',
    },

    // Скидання та експорт
    resetAllColors: 'Скинути всі кольори до акценту',
    resetAllSettings: 'Скинути всі налаштування',
  },

  // Списки
  lists: {
    newList: 'Новий список',
    listName: 'Назва списку',
    addLink: 'Додати посилання',
    linkName: 'Назва посилання',
    linkUrl: 'URL посилання',
    editList: 'Редагувати список',
    deleteList: 'Видалити список',
    listIcon: 'Іконка списку',
    listColor: 'Колір списку',
    linkColor: 'Колір посилання',
    hideIcons: 'Приховати іконки посилань',
    openInNewWindow: 'Відкрити у новому вікні',
    copyLink: 'Скопіювати посилання',
    editLink: 'Редагувати посилання',
    deleteLink: 'Видалити посилання',
    title: 'Заголовок',
    url: 'URL',
    addNewList: 'Додати список',
    iconColor: 'Колір іконки',
  },

  // Швидкі посилання
  fastLinks: {
    newFastLink: 'Нове швидке посилання',
    fastLinkName: 'Назва швидкого посилання',
    fastLinkUrl: 'URL',
    editFastLink: 'Редагувати швидке посилання',
    deleteFastLink: 'Видалити швидке посилання',
    fastLinkColor: 'Колір швидкого посилання',
  },

  // Пошук
  search: {
    placeholder: 'Пошук...',
    searchWith: 'Шукати за допомогою',
    google: 'Google',
    yandex: 'Яндекс',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Шукати шрифти...',
    fontsNotFound: 'Шрифти не знайдено',
    searchInGoogle: 'Шукати в Google...',
    searchInYandex: 'Шукати в Яндекс...',
    searchInBing: 'Шукати в Bing...',
    searchInDuckDuckGo: 'Шукати в DuckDuckGo...',
    searchInYahoo: 'Шукати в Yahoo...',
    searchInBaidu: 'Шукати в Baidu...',
    searchInStartpage: 'Шукати в Startpage...',
    searchInSearX: 'Шукати в SearX...',
    searchInEcosia: 'Шукати в Ecosia...',
    searchInBrave: 'Шукати в Brave...',
  },

  // Помилки
  errors: {
    invalidUrl: 'Недійсний URL',
    nameRequired: 'Ім\'я є обов\'язковим',
    urlRequired: 'URL є обов\'язковим',
    fileUploadError: 'Помилка завантаження файлу',
    settingsImportError: 'Помилка імпорту налаштувань',
    settingsExportError: 'Помилка експорту налаштувань',
    invalidImageUrl: 'Недійсний URL зображення',
    imageValidationError: 'Помилка перевірки зображення',
    imageLoadFailed: 'Не вдалося завантажити зображення',
    imageTooSmall: 'Зображення занадто мале',
    settingsImported: 'Налаштування успішно імпортовано! Сторінка буде перезавантажена, щоб застосувати всі зміни.',
    settingsExported: 'Налаштування успішно експортовано',
    parseError: 'Помилка аналізу:',
    invalidFileFormat: 'Помилка читання файлу налаштувань. Переконайтеся, що файл має правильний формат.',
    importError: 'Помилка імпорту налаштувань. Перевірте консоль для деталей.',
    exportError: 'Помилка експорту налаштувань. Перевірте консоль для деталей.',
    resetConfirm: 'Ви впевнені, що хочете скинути всі налаштування до значень за замовчуванням? Цю дію неможливо скасувати. Усі користувацькі налаштування, включаючи фони, списки та швидкі посилання, будуть повністю видалені з localStorage.',
    deleteListConfirm: 'Ви впевнені, що хочете видалити цей список? Цю дію неможливо скасувати.',
    deleteLinkConfirm: 'Ви впевнені, що хочете видалити це посилання?',
    loadingError: 'Помилка завантаження',
    backgroundLoadError: 'Не вдалося завантажити фонове зображення',
    criticalError: 'Виникла критична помилка. Сторінка буде перезавантажена.',
    jsError: 'Помилка JavaScript',
    promiseRejection: 'Необроблена помилка Promise',
  },

  // Підказки
  tooltips: {
    settings: 'Налаштування',
    addList: 'Додати список',
    addFastLink: 'Додати швидке посилання',
    editItem: 'Редагувати',
    deleteItem: 'Видалити',
    updateItem: 'Оновити',
    resetColor: 'Скинути колір',
    openAllLinks: 'Відкрити всі посилання у нових вкладках',
    addLink: 'Додати посилання',
    openLink: 'Відкрити посилання',
    copyLink: 'Скопіювати посилання',
    dragToReorder: 'Перетягніть, щоб змінити порядок',
    exportSettings: 'Експортувати всі налаштування у файл',
    importSettings: 'Імпортувати налаштування з файлу',
    resetAllSettings: 'Скинути всі налаштування',
    closeSettings: 'Закрити налаштування',
    generateColor: 'Згенерувати колір на основі зображення',
    applyAccentColor: 'Застосувати колір акценту до всіх елементів',
    addRandomPhoto: 'Додати випадкове фото з інтернету',
    deleteImage: 'Видалити зображення',
    addColor: 'Додати колір',
    deleteColor: 'Видалити колір',
    resetFilters: 'Скинути фільтри',
    expandFilters: 'Розгорнути фільтри',
    collapseFilters: 'Згорнути фільтри',
    stopAutoSwitch: 'Зупинити автоматичне перемикання',
    startAutoSwitch: 'Запустити автоматичне перемикання',
  },

  // Час
  time: {
    seconds: 'с',
    minutes: 'хв',
    hours: 'год',
    days: 'дні',
    months: ['Січень', 'Лютий', 'Березень', 'Квітень', 'Травень', 'Червень', 'Липень', 'Серпень', 'Вересень', 'Жовтень', 'Листопад', 'Грудень'],
    weekdays: ['Неділя', 'Понеділок', 'Вівторок', 'Середа', 'Четвер', 'П\'ятниця', 'Субота'],
  },

  // Одиниці виміру
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'с',
    minutes: 'хв',
    small: 'Малий',
    medium: 'Середній',
    large: 'Великий',
  },

  // Діалоги
  dialogs: {
    newName: 'Нове ім\'я',
    newListName: 'Назва нового списку',
    linkTitle: 'Назва посилання',
    linkColor: 'Колір посилання',
    separatorColor: 'Колір роздільника',
    titleColor: 'Колір заголовка',
  },

  // ARIA мітки
  ariaLabels: {
    settings: 'Налаштування',
    resetAllSettings: 'Скинути всі налаштування',
    closeSettings: 'Закрити налаштування',
    applyAccentColor: 'Застосувати колір акценту до всіх елементів',
    addList: 'Додати список',
    addRandomPhoto: 'Додати випадкове фото',
    deleteImage: 'Видалити зображення',
    addColor: 'Додати колір',
    deleteColor: 'Видалити колір',
    resetFilters: 'Скинути фільтри',
    expandFilters: 'Розгорнути фільтри',
    collapseFilters: 'Згорнути фільтри',
  },

  // Радіус рамки
  radius: {
    none: 'Немає',
    small: 'Малий',
    medium: 'Середній',
    large: 'Великий',
    full: 'Повний',
  },

  // Категорії шрифтів
  fonts: {
    system: 'Системний',
    serif: 'Із зарубками',
    monospace: 'Моноширинний',
    display: 'Дисплейний',
    pixel: 'Піксельний',
    terminal: 'Термінальний',
    modern: 'Сучасний',
    decorative: 'Декоративний',
  },
};