export const pt = {
  // Geral
  common: {
    apply: 'Aplicar',
    cancel: 'Cancelar',
    save: '<PERSON><PERSON>',
    delete: 'Excluir',
    edit: 'Editar',
    update: 'Atualizar',
    reset: 'Redefinir',
    resetIcon: 'Redefinir Ícone',
    add: 'Ad<PERSON>onar',
    remove: 'Remover',
    close: '<PERSON><PERSON><PERSON>',
    open: 'Abrir',
    enable: 'Ativar',
    disable: '<PERSON>ati<PERSON>',
    show: 'Mostrar',
    hide: 'Ocultar',
    yes: 'Sim',
    no: 'Não',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
    warning: 'Aviso',
    info: 'Informação',
    create: 'Criar',
    enterName: 'Digite o nome...',
    sidebar: 'Barra Lateral',
    sidebarDescription: 'Barra lateral com configurações e conteúdo adicional',
  },

  // Configurações
  settings: {
    title: 'Configurações',
    description: '<PERSON>qui estarão as configurações da extensão (tema, aparência, opções, etc.)',
    basicSettings: 'Configurações Básicas',
    clockSettings: 'Configurações do Relógio',
    searchSettings: 'Mecanismo de Busca',
    fastLinksSettings: 'Links Rápidos',
    backgroundSettings: 'Plano de Fundo',
    presetsSettings: 'Predefinições',
    listsSettings: 'Configurações de Listas',

    // Configurações básicas
    language: 'Idioma da Interface',
    exportSettings: 'Exportar',
    importSettings: 'Importar',
    theme: 'Tema',
    accentColor: 'Cor de Destaque',
    borderRadius: 'Raio da Borda',
    cleanMode: 'Modo Limpo',
    cleanModeDescription: 'Oculta os botões de abrir lista',
    colorScheme: 'Esquema de Cores',
    borderRounding: 'Arredondamento de Elementos',
    
    // Relógio
    showClock: 'Mostrar Relógio',
    showSeconds: 'Mostrar Segundos',
    showDate: 'Mostrar Data',
    clockSize: 'Tamanho do Relógio',
    clockColor: 'Cor do Relógio e Data',
    
    // Busca
    showSearch: 'Mostrar Busca',
    searchEngine: 'Mecanismo de Busca',
    searchSize: 'Tamanho da Busca',
    searchBackgroundColor: 'Cor de Fundo da Busca',
    searchBorderColor: 'Cor da Borda da Busca',
    searchTextColor: 'Cor do Texto da Busca',
    searchBackdropBlur: 'Desfoque de Fundo da Busca',
    showSearchEngine: 'Mostrar Mecanismo de Busca',
    backgroundBlur: 'Desfoque de Fundo',
    
    // Links rápidos
    showFastLinks: 'Mostrar Links Rápidos',
    fastLinksColumns: 'Número de Colunas',
    fastLinksSize: 'Tamanho do Link Rápido',
    fastLinksBackdropBlur: 'Desfoque de Fundo do Link Rápido',
    addFastLink: 'Adicionar Link Rápido',
    textColor: 'Cor do Texto do Título',
    backdropColor: 'Cor do Plano de Fundo',
    iconBackgroundColor: 'Cor de Fundo do Ícone',
    colors: 'Cores',
    display: 'Exibição',
    hideIcons: 'Ocultar Ícones',
    hideText: 'Ocultar Texto',
    backgroundEffects: 'Efeitos de Fundo do Ícone',

    // Listas
    showLists: 'Mostrar Listas de Links',
    listsColumns: 'Número de Colunas',
    listsBackgroundColor: 'Cor de Fundo das Listas',
    listsBackdropBlur: 'Desfoque de Fundo das Listas',
    addNewList: 'Adicionar Nova Lista',
    listBackground: 'Plano de Fundo da Lista',
    backgroundColor: 'Cor de Fundo',
    borderColor: 'Cor da Borda',
    borderThickness: 'Espessura da Borda',
    hideBorder: 'Ocultar Borda',
    hideBackground: 'Ocultar Fundo',
    separator: 'Separador',
    separatorColor: 'Cor do Separador',
    separatorThickness: 'Espessura do Separador',
    hideSeparator: 'Ocultar Separador',
    colorsAndIcons: 'Cores e Ícones',
    titleColor: 'Cor do Título',
    linkColor: 'Cor do Link',
    hideLinkIcons: 'Ocultar Ícones de Link',
    listGrid: 'Grade da Lista',
    columnsCount: 'Contagem de Colunas',
    listManagement: 'Gerenciamento de Listas',
    
    // Plano de Fundo
    backgroundType: 'Tipo de Plano de Fundo',
    solidColor: 'Cor Sólida',
    gradient: 'Gradiente',
    image: 'Imagem',
    brightness: 'Brilho',
    contrast: 'Contraste',
    saturation: 'Saturação',
    blur: 'Desfoque',
    hueRotate: 'Rotação de Matiz',
    sepia: 'Sépia',
    grayscale: 'Escala de Cinza',
    invert: 'Inverter',
    shadowOverlay: 'Sobreposição de Sombra',
    parallaxEffect: 'Efeito Parallax',
    autoSwitch: 'Troca Automática',
    switchInterval: 'Intervalo de Troca',
    addImages: 'Adicionar Imagens',
    uploadImages: 'Carregar Imagens',
    addImage: 'Adicionar Imagem',
    checking: 'Verificando...',
    addRandomPhoto: 'Adicionar foto aleatória da internet',
    parallaxDescription: 'A imagem de fundo seguirá o movimento do mouse',
    shadowBottom: 'Sombra Inferior',
    shadowDescription: 'Sombra gradiente para melhor visibilidade da lista',
    intensity: 'Intensidade:',
    height: 'Altura:',
    gallery: 'Galeria',
    startAutoSwitch: 'Iniciar troca automática',
    stopAutoSwitch: 'Parar troca automática',
    onLoad: 'Ao Carregar',
    daily: 'Diário',
    deleteImage: 'Excluir Imagem',
    
    // Gradientes
    gradientType: 'Tipo de Gradiente',
    addColor: 'Adicionar Cor',
    deleteColor: 'Excluir Cor',
    direction: 'Direção',
    position: 'Posição',
    customCSS: 'Gradiente CSS (opcional)',
    customCSSDescription: 'Insira a string de gradiente CSS para aplicar em vez da configuração manual',
    backgroundFilters: 'Filtros de Fundo',
    resetFilters: 'Redefinir Filtros',
    expandFilters: 'Expandir Filtros',
    collapseFilters: ' Recolher Filtros',
    color: 'Cor',
    filters: 'Filtros de Fundo',
    font: 'Fonte',
    right: 'Direita',
    left: 'Esquerda',
    bottom: 'Inferior',
    top: 'Superior',
    bottomRight: 'Inferior Direita',
    bottomLeft: 'Inferior Esquerda',
    topRight: 'Superior Direita',
    topLeft: 'Superior Esquerda',
    center: 'Centro',
    gradientDirection: 'Direção do Gradiente',
    gradientColor1: 'Cor 1',
    gradientColor2: 'Cor 2',
    gradientColor3: 'Cor 3',
    gradientColor4: 'Cor 4',
    gradientColor5: 'Cor 5',
    gradientColor6: 'Cor 6',
    customGradient: 'Gradiente Personalizado',
    customGradientCSS: 'CSS Personalizado',
    gradientPreview: 'Pré-visualizar',
    linear: 'Linear',
    radial: 'Radial',
    conic: 'Cônico',
    toRight: 'Para a Direita',
    toLeft: 'Para a Esquerda',
    toBottom: 'Para Baixo',
    toTop: 'Para Cima',
    toBottomRight: 'Para Baixo e Direita',
    toBottomLeft: 'Para Baixo e Esquerda',
    toTopRight: 'Para Cima e Direita',
    toTopLeft: 'Para Cima e Esquerda',
    circle: 'Círculo',
    ellipse: 'Elipse',
    fromCenter: 'Do Centro',
    
    // Filtros de imagem
    imageFilters: 'Filtros de Imagem',
    
    // Predefinições
    presets: 'Predefinições',
    createPreset: 'Criar Predefinição',
    presetName: 'Nome da Predefinição',
    noPresets: 'Nenhuma predefinição criada. Crie sua primeira predefinição para alternar rapidamente entre as configurações.',
    renamePreset: 'Renomear Predefinição',
    updatePreset: 'Atualizar Predefinição com as Configurações Atuais',
    deletePreset: 'Excluir Predefinição',
    createNewPreset: 'Criar Nova Predefinição',
    presetDescription: 'A predefinição salvará as configurações atuais de cor, fonte e plano de fundo.',

    // Categorias de fontes
    fontCategories: {
      sansSerif: 'Sem Serifa',
      serif: 'Serifa',
      monospace: 'Monoespaçada',
      display: 'Display',
      handwriting: 'Manuscrita',
      pixel: 'Pixel',
      terminal: 'Terminal',
    },
    
    // Traduções
    aiTranslationsDisclaimer: 'Todas as traduções são geradas por IA',

    // Nomes e categorias de fontes
    fonts: {
      systemFont: 'Fonte do Sistema',
      systemFonts: 'Fontes do Sistema',
      serifFonts: 'Fontes com Serifa',
      monospaceFonts: 'Fontes Monoespaçadas',
      displayFonts: 'Fontes de Display',
      pixelFonts: 'Fontes Pixel',
      terminalFonts: 'Fontes de Terminal',
      modernFonts: 'Fontes Modernas Adicionais',
      decorativeFonts: 'Fontes Decorativas Adicionais',
    },

    // Redefinir e exportar
    resetAllColors: 'Redefinir Todas as Cores para o Destaque',
    resetAllSettings: 'Redefinir Todas as Configurações',
  },

  // Listas
  lists: {
    newList: 'Nova Lista',
    listName: 'Nome da Lista',
    addLink: 'Adicionar Link',
    linkName: 'Nome do Link',
    linkUrl: 'URL do Link',
    editList: 'Editar Lista',
    deleteList: 'Excluir Lista',
    listIcon: 'Ícone da Lista',
    listColor: 'Cor da Lista',
    linkColor: 'Cor do Link',
    hideIcons: 'Ocultar Ícones de Link',
    openInNewWindow: 'Abrir em Nova Janela',
    copyLink: 'Copiar Link',
    editLink: 'Editar Link',
    deleteLink: 'Excluir Link',
    title: 'Título',
    url: 'URL',
    addNewList: 'Adicionar Lista',
    iconColor: 'Cor do Ícone',
  },

  // Links rápidos
  fastLinks: {
    newFastLink: 'Novo Link Rápido',
    fastLinkName: 'Nome do Link Rápido',
    fastLinkUrl: 'URL',
    editFastLink: 'Editar Link Rápido',
    deleteFastLink: 'Excluir Link Rápido',
    fastLinkColor: 'Cor do Link Rápido',
  },

  // Busca
  search: {
    placeholder: 'Buscar...',
    searchWith: 'Buscar com',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Buscar fontes...',
    fontsNotFound: 'Fontes não encontradas',
    searchInGoogle: 'Buscar no Google...',
    searchInYandex: 'Buscar no Yandex...',
    searchInBing: 'Buscar no Bing...',
    searchInDuckDuckGo: 'Buscar no DuckDuckGo...',
    searchInYahoo: 'Buscar no Yahoo...',
    searchInBaidu: 'Buscar no Baidu...',
    searchInStartpage: 'Buscar no Startpage...',
    searchInSearX: 'Buscar no SearX...',
    searchInEcosia: 'Buscar no Ecosia...',
    searchInBrave: 'Buscar no Brave...',
  },

  // Erros
  errors: {
    invalidUrl: 'URL inválida',
    nameRequired: 'Nome é obrigatório',
    urlRequired: 'URL é obrigatória',
    fileUploadError: 'Erro no upload do arquivo',
    settingsImportError: 'Erro na importação das configurações',
    settingsExportError: 'Erro na exportação das configurações',
    invalidImageUrl: 'URL de imagem inválida',
    imageValidationError: 'Erro de validação da imagem',
    imageLoadFailed: 'Falha ao carregar imagem',
    imageTooSmall: 'A imagem é muito pequena',
    settingsImported: 'Configurações importadas com sucesso! A página será recarregada para aplicar todas as alterações.',
    settingsExported: 'Configurações exportadas com sucesso',
    parseError: 'Erro de análise:',
    invalidFileFormat: 'Erro ao ler o arquivo de configurações. Certifique-se de que o arquivo tenha o formato correto.',
    importError: 'Erro ao importar configurações. Verifique o console para detalhes.',
    exportError: 'Erro ao exportar configurações. Verifique o console para detalhes.',
    resetConfirm: 'Tem certeza de que deseja redefinir todas as configurações para os valores padrão? Esta ação não pode ser desfeita. Todas as configurações personalizadas, incluindo planos de fundo, listas e links rápidos, serão completamente removidas do localStorage.',
    deleteListConfirm: 'Tem certeza de que deseja excluir esta lista? Esta ação não pode ser desfeita.',
    deleteLinkConfirm: 'Tem certeza de que deseja excluir este link?',
    loadingError: 'Erro de carregamento',
    backgroundLoadError: 'Falha ao carregar imagem de fundo',
    criticalError: 'Ocorreu um erro crítico. A página será recarregada.',
    jsError: 'Erro JavaScript',
    promiseRejection: 'Erro de Promessa Não Tratado',
  },

  // Dicas de ferramentas
  tooltips: {
    settings: 'Configurações',
    addList: 'Adicionar Lista',
    addFastLink: 'Adicionar Link Rápido',
    editItem: 'Editar',
    deleteItem: 'Excluir',
    updateItem: 'Atualizar',
    resetColor: 'Redefinir Cor',
    openAllLinks: 'Abrir todos os links em novas abas',
    addLink: 'Adicionar Link',
    openLink: 'Abrir Link',
    copyLink: 'Copiar Link',
    dragToReorder: 'Arrastar para reordenar',
    exportSettings: 'Exportar todas as configurações para arquivo',
    importSettings: 'Importar configurações de arquivo',
    resetAllSettings: 'Redefinir Todas as Configurações',
    closeSettings: 'Fechar Configurações',
    generateColor: 'Gerar cor com base na imagem',
    applyAccentColor: 'Aplicar cor de destaque a todos os elementos',
    addRandomPhoto: 'Adicionar foto aleatória da internet',
    deleteImage: 'Excluir Imagem',
    addColor: 'Adicionar Cor',
    deleteColor: 'Excluir Cor',
    resetFilters: 'Redefinir Filtros',
    expandFilters: 'Expandir Filtros',
    collapseFilters: ' Recolher Filtros',
    stopAutoSwitch: 'Parar troca automática',
    startAutoSwitch: 'Iniciar troca automática',
  },

  // Tempo
  time: {
    seconds: 'seg',
    minutes: 'min',
    hours: 'h',
    days: 'dias',
    months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
    weekdays: ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'],
  },

  // Unidades
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'seg',
    minutes: 'min',
    small: 'Pequeno',
    medium: 'Médio',
    large: 'Grande',
  },

  // Diálogos
  dialogs: {
    newName: 'Novo Nome',
    newListName: 'Nome da Nova Lista',
    linkTitle: 'Título do Link',
    linkColor: 'Cor do Link',
    separatorColor: 'Cor do Separador',
    titleColor: 'Cor do Título',
  },

  // Rótulos ARIA
  ariaLabels: {
    settings: 'Configurações',
    resetAllSettings: 'Redefinir Todas as Configurações',
    closeSettings: 'Fechar Configurações',
    applyAccentColor: 'Aplicar cor de destaque a todos os elementos',
    addList: 'Adicionar Lista',
    addRandomPhoto: 'Adicionar foto aleatória',
    deleteImage: 'Excluir Imagem',
    addColor: 'Adicionar Cor',
    deleteColor: 'Excluir Cor',
    resetFilters: 'Redefinir Filtros',
    expandFilters: 'Expandir Filtros',
    collapseFilters: ' Recolher Filtros',
  },

  // Raio da borda
  radius: {
    none: 'Nenhum',
    small: 'Pequeno',
    medium: 'Médio',
    large: 'Grande',
    full: 'Total',
  },

  // Categorias de fontes
  fonts: {
    system: 'Sistema',
    serif: 'Serifa',
    monospace: 'Monoespaçada',
    display: 'Display',
    pixel: 'Pixel',
    terminal: 'Terminal',
    modern: 'Moderna',
    decorative: 'Decorativa',
  },
};