// Diccionario de traducción al español
export const es = {
  // General
  common: {
    apply: 'Aplicar',
    cancel: 'Cancelar',
    save: 'Guardar',
    delete: 'Eliminar',
    edit: 'Editar',
    update: 'Actualizar',
    reset: 'Restablecer',
    resetIcon: 'Restablecer icono',
    add: 'Aña<PERSON>',
    remove: 'Quitar',
    close: 'Cerrar',
    open: 'Abrir',
    enable: 'Habilitar',
    disable: 'Deshabilitar',
    show: 'Mostrar',
    hide: 'Ocultar',
    yes: 'Sí',
    no: 'No',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    warning: 'Advertencia',
    info: 'Información',
    create: 'Crear',
    enterName: 'Introducir nombre...',
    sidebar: 'Barra lateral',
    sidebarDescription: 'Barra lateral con ajustes y contenido adicional',
  },

  // Settings
  settings: {
    title: 'Ajustes',
    description: 'Aquí estarán los ajustes de la extensión (tema, apariencia, opciones, etc.)',
    basicSettings: 'Ajustes básicos',
    clockSettings: 'Ajustes del reloj',
    searchSettings: 'Motor de búsqueda',
    fastLinksSettings: 'Enlaces rápidos',
    backgroundSettings: 'Fondo',
    presetsSettings: 'Preajustes',
    listsSettings: 'Ajustes de listas',

    // Basic settings
    language: 'Idioma de la interfaz',
    exportSettings: 'Exportar',
    importSettings: 'Importar',
    theme: 'Tema',
    accentColor: 'Color de acento',
    borderRadius: 'Radio del borde',
    cleanMode: 'Modo limpio',
    cleanModeDescription: 'Oculta los botones para abrir listas',
    colorScheme: 'Esquema de color',
    borderRounding: 'Redondeo de elementos',
    
    // Clock
    showClock: 'Mostrar reloj',
    showSeconds: 'Mostrar segundos',
    showDate: 'Mostrar fecha',
    clockSize: 'Tamaño del reloj',
    clockColor: 'Color del reloj y fecha',
    
    // Search
    showSearch: 'Mostrar búsqueda',
    searchEngine: 'Motor de búsqueda',
    searchSize: 'Tamaño de búsqueda',
    searchBackgroundColor: 'Color de fondo de búsqueda',
    searchBorderColor: 'Color del borde de búsqueda',
    searchTextColor: 'Color del texto de búsqueda',
    searchBackdropBlur: 'Desenfoque del fondo de búsqueda',
    showSearchEngine: 'Mostrar motor de búsqueda',
    backgroundBlur: 'Desenfoque de fondo',
    
    // Quick links
    showFastLinks: 'Mostrar enlaces rápidos',
    fastLinksColumns: 'Número de columnas',
    fastLinksSize: 'Tamaño de enlace rápido',
    fastLinksBackdropBlur: 'Desenfoque de fondo de enlaces rápidos',
    addFastLink: 'Añadir enlace rápido',
    textColor: 'Color del texto del título',
    backdropColor: 'Color de fondo',
    iconBackgroundColor: 'Color de fondo del icono',
    colors: 'Colores',
    display: 'Visualización',
    hideIcons: 'Ocultar iconos',
    hideText: 'Ocultar texto',
    backgroundEffects: 'Efectos de fondo de icono',

    // Lists
    showLists: 'Mostrar listas de enlaces',
    listsColumns: 'Número de columnas',
    listsBackgroundColor: 'Color de fondo de listas',
    listsBackdropBlur: 'Desenfoque de fondo de listas',
    addNewList: 'Añadir nueva lista',
    listBackground: 'Fondo de lista',
    backgroundColor: 'Color de fondo',
    borderColor: 'Color del borde',
    borderThickness: 'Grosor del borde',
    hideBorder: 'Ocultar borde',
    hideBackground: 'Ocultar fondo',
    separator: 'Separador',
    separatorColor: 'Color del separador',
    separatorThickness: 'Grosor del separador',
    hideSeparator: 'Ocultar separador',
    colorsAndIcons: 'Colores e iconos',
    titleColor: 'Color del título',
    linkColor: 'Color del enlace',
    hideLinkIcons: 'Ocultar iconos de enlace',
    listGrid: 'Cuadrícula de lista',
    columnsCount: 'Número de columnas',
    listManagement: 'Gestión de listas',
    
    // Background
    backgroundType: 'Tipo de fondo',
    solidColor: 'Color sólido',
    gradient: 'Degradado',
    image: 'Imagen',
    brightness: 'Brillo',
    contrast: 'Contraste',
    saturation: 'Saturación',
    blur: 'Desenfoque',
    hueRotate: 'Rotación de tono',
    sepia: 'Sépia',
    grayscale: 'Escala de grises',
    invert: 'Invertir',
    shadowOverlay: 'Superposición de sombra',
    parallaxEffect: 'Efecto paralaje',
    autoSwitch: 'Cambio automático',
    switchInterval: 'Intervalo de cambio',
    addImages: 'Añadir imágenes',
    uploadImages: 'Subir imágenes',
    addImage: 'Añadir imagen',
    checking: 'Comprobando...',
    addRandomPhoto: 'Añadir foto aleatoria de internet',
    parallaxDescription: 'La imagen de fondo seguirá el movimiento del ratón',
    shadowBottom: 'Sombra inferior',
    shadowDescription: 'Sombra degradada para mejor visibilidad de listas',
    intensity: 'Intensidad:',
    height: 'Altura:',
    gallery: 'Galería',
    startAutoSwitch: 'Iniciar cambio automático',
    stopAutoSwitch: 'Detener cambio automático',
    onLoad: 'Al cargar',
    daily: 'Diario',
    deleteImage: 'Eliminar imagen',
    
    // Gradients
    gradientType: 'Tipo de degradado',
    addColor: 'Añadir color',
    deleteColor: 'Eliminar color',
    direction: 'Dirección',
    position: 'Posición',
    customCSS: 'Degradado CSS (opcional)',
    customCSSDescription: 'Introduce cadena CSS de degradado para aplicar en lugar de configuración manual',
    backgroundFilters: 'Filtros de fondo',
    resetFilters: 'Restablecer filtros',
    expandFilters: 'Expandir filtros',
    collapseFilters: 'Contraer filtros',
    color: 'Color',
    filters: 'Filtros de fondo',
    font: 'Fuente',
    right: 'Derecha',
    left: 'Izquierda',
    bottom: 'Inferior',
    top: 'Superior',
    bottomRight: 'Inferior derecha',
    bottomLeft: 'Inferior izquierda',
    topRight: 'Superior derecha',
    topLeft: 'Superior izquierda',
    center: 'Centro',
    gradientDirection: 'Dirección del degradado',
    gradientColor1: 'Color 1',
    gradientColor2: 'Color 2',
    gradientColor3: 'Color 3',
    gradientColor4: 'Color 4',
    gradientColor5: 'Color 5',
    gradientColor6: 'Color 6',
    customGradient: 'Degradado personalizado',
    customGradientCSS: 'CSS personalizado',
    gradientPreview: 'Vista previa',
    linear: 'Lineal',
    radial: 'Radial',
    conic: 'Cónico',
    toRight: 'A la derecha',
    toLeft: 'A la izquierda',
    toBottom: 'Hacia abajo',
    toTop: 'Hacia arriba',
    toBottomRight: 'Abajo derecha',
    toBottomLeft: 'Abajo izquierda',
    toTopRight: 'Arriba derecha',
    toTopLeft: 'Arriba izquierda',
    circle: 'Círculo',
    ellipse: 'Elipse',
    fromCenter: 'Desde el centro',
    
    // Image filters
    imageFilters: 'Filtros de imagen',
    
    // Presets
    presets: 'Preajustes',
    createPreset: 'Crear preajuste',
    presetName: 'Nombre del preajuste',
    noPresets: 'No se han creado preajustes. Crea tu primer preajuste para cambiar rápidamente entre configuraciones.',
    renamePreset: 'Renombrar preajuste',
    updatePreset: 'Actualizar preajuste con ajustes actuales',
    deletePreset: 'Eliminar preajuste',
    createNewPreset: 'Crear nuevo preajuste',
    presetDescription: 'El preajuste guardará los ajustes actuales de color, fuente y fondo.',

    // Font categories
    fontCategories: {
      sansSerif: 'Sans Serif',
      serif: 'Serif',
      monospace: 'Monoespaciado',
      display: 'Display',
      handwriting: 'Manuscrita',
      pixel: 'Pixel',
      terminal: 'Terminal',
    },
    
    // Translations
    aiTranslationsDisclaimer: 'Todas las traducciones son generadas por IA',

    // Font names and categories
    fonts: {
      systemFont: 'Fuente del sistema',
      systemFonts: 'Fuentes del sistema',
      serifFonts: 'Fuentes Serif',
      monospaceFonts: 'Fuentes monoespaciadas',
      displayFonts: 'Fuentes display',
      pixelFonts: 'Fuentes pixel',
      terminalFonts: 'Fuentes terminal',
      modernFonts: 'Fuentes modernas adicionales',
      decorativeFonts: 'Fuentes decorativas adicionales',
    },

    // Reset and export
    resetAllColors: 'Restablecer todos los colores al acento',
    resetAllSettings: 'Restablecer todos los ajustes',
  },

  // Lists
  lists: {
    newList: 'Nueva lista',
    listName: 'Nombre de lista',
    addLink: 'Añadir enlace',
    linkName: 'Nombre del enlace',
    linkUrl: 'URL del enlace',
    editList: 'Editar lista',
    deleteList: 'Eliminar lista',
    listIcon: 'Icono de lista',
    listColor: 'Color de lista',
    linkColor: 'Color del enlace',
    hideIcons: 'Ocultar iconos de enlace',
    openInNewWindow: 'Abrir en nueva ventana',
    copyLink: 'Copiar enlace',
    editLink: 'Editar enlace',
    deleteLink: 'Eliminar enlace',
    title: 'Título',
    url: 'URL',
    addNewList: 'Añadir lista',
    iconColor: 'Color del icono',
  },

  // Quick links
  fastLinks: {
    newFastLink: 'Nuevo enlace rápido',
    fastLinkName: 'Nombre del enlace rápido',
    fastLinkUrl: 'URL',
    editFastLink: 'Editar enlace rápido',
    deleteFastLink: 'Eliminar enlace rápido',
    fastLinkColor: 'Color del enlace rápido',
  },

  // Search
  search: {
    placeholder: 'Buscar...',
    searchWith: 'Buscar con',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Buscar fuentes...',
    fontsNotFound: 'Fuentes no encontradas',
    searchInGoogle: 'Buscar en Google...',
    searchInYandex: 'Buscar en Yandex...',
    searchInBing: 'Buscar en Bing...',
    searchInDuckDuckGo: 'Buscar en DuckDuckGo...',
    searchInYahoo: 'Buscar en Yahoo...',
    searchInBaidu: 'Buscar en Baidu...',
    searchInStartpage: 'Buscar en Startpage...',
    searchInSearX: 'Buscar en SearX...',
    searchInEcosia: 'Buscar en Ecosia...',
    searchInBrave: 'Buscar en Brave...',
  },

  // Errors
  errors: {
    invalidUrl: 'URL inválida',
    nameRequired: 'Se requiere nombre',
    urlRequired: 'Se requiere URL',
    fileUploadError: 'Error al subir archivo',
    settingsImportError: 'Error al importar ajustes',
    settingsExportError: 'Error al exportar ajustes',
    invalidImageUrl: 'URL de imagen inválida',
    imageValidationError: 'Error de validación de imagen',
    imageLoadFailed: 'Error al cargar imagen',
    imageTooSmall: 'Imagen demasiado pequeña',
    settingsImported: '¡Ajustes importados correctamente! La página se recargará para aplicar los cambios.',
    settingsExported: 'Ajustes exportados correctamente',
    parseError: 'Error de análisis:',
    invalidFileFormat: 'Error al leer el archivo de ajustes. Asegúrate de que el formato es correcto.',
    importError: 'Error al importar ajustes. Consulta la consola para detalles.',
    exportError: 'Error al exportar ajustes. Consulta la consola para detalles.',
    resetConfirm: '¿Seguro que deseas restablecer todos los ajustes a los valores predeterminados? Esta acción no se puede deshacer. Todos los ajustes personalizados, incluidos fondos, listas y enlaces rápidos, se eliminarán completamente del localStorage.',
    deleteListConfirm: '¿Seguro que deseas eliminar esta lista? Esta acción no se puede deshacer.',
    deleteLinkConfirm: '¿Seguro que deseas eliminar este enlace?',
    loadingError: 'Error de carga',
    backgroundLoadError: 'Error al cargar imagen de fondo',
    criticalError: 'Ocurrió un error crítico. La página se recargará.',
    jsError: 'Error de JavaScript',
    promiseRejection: 'Error de promesa no manejado',
  },

  // Tooltips
  tooltips: {
    settings: 'Ajustes',
    addList: 'Añadir lista',
    addFastLink: 'Añadir enlace rápido',
    editItem: 'Editar',
    deleteItem: 'Eliminar',
    updateItem: 'Actualizar',
    resetColor: 'Restablecer color',
    openAllLinks: 'Abrir todos los enlaces en nuevas pestañas',
    addLink: 'Añadir enlace',
    openLink: 'Abrir enlace',
    copyLink: 'Copiar enlace',
    dragToReorder: 'Arrastrar para reordenar',
    exportSettings: 'Exportar todos los ajustes a archivo',
    importSettings: 'Importar ajustes desde archivo',
    resetAllSettings: 'Restablecer todos los ajustes',
    closeSettings: 'Cerrar ajustes',
    generateColor: 'Generar color basado en imagen',
    applyAccentColor: 'Aplicar color de acento a todos los elementos',
    addRandomPhoto: 'Añadir foto aleatoria de internet',
    deleteImage: 'Eliminar imagen',
    addColor: 'Añadir color',
    deleteColor: 'Eliminar color',
    resetFilters: 'Restablecer filtros',
    expandFilters: 'Expandir filtros',
    collapseFilters: 'Contraer filtros',
    stopAutoSwitch: 'Detener cambio automático',
    startAutoSwitch: 'Iniciar cambio automático',
  },

  // Time
  time: {
    seconds: 'seg',
    minutes: 'min',
    hours: 'h',
    days: 'días',
    months: ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'],
    weekdays: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
  },

  // Units
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'seg',
    minutes: 'min',
    small: 'Pequeño',
    medium: 'Mediano',
    large: 'Grande',
  },

  // Dialogs
  dialogs: {
    newName: 'Nuevo nombre',
    newListName: 'Nuevo nombre de lista',
    linkTitle: 'Título del enlace',
    linkColor: 'Color del enlace',
    separatorColor: 'Color del separador',
    titleColor: 'Color del título',
  },

  // ARIA labels
  ariaLabels: {
    settings: 'Ajustes',
    resetAllSettings: 'Restablecer todos los ajustes',
    closeSettings: 'Cerrar ajustes',
    applyAccentColor: 'Aplicar color de acento a todos los elementos',
    addList: 'Añadir lista',
    addRandomPhoto: 'Añadir foto aleatoria',
    deleteImage: 'Eliminar imagen',
    addColor: 'Añadir color',
    deleteColor: 'Eliminar color',
    resetFilters: 'Restablecer filtros',
    expandFilters: 'Expandir filtros',
    collapseFilters: 'Contraer filtros',
  },

  // Border radius
  radius: {
    none: 'Ninguno',
    small: 'Pequeño',
    medium: 'Mediano',
    large: 'Grande',
    full: 'Completo',
  },

  // Font categories
  fonts: {
    system: 'Sistema',
    serif: 'Serif',
    monospace: 'Monoespaciada',
    display: 'Display',
    pixel: 'Pixel',
    terminal: 'Terminal',
    modern: 'Moderna',
    decorative: 'Decorativa',
  },
};