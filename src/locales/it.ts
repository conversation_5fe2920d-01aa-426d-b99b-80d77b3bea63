export const it = {
  // General
  common: {
    apply: 'Applica',
    cancel: '<PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    edit: 'Modi<PERSON>',
    update: 'Aggiorna',
    reset: 'Ripristina',
    resetIcon: 'Ripristina Icona',
    add: 'Aggiungi',
    remove: 'Rimuo<PERSON>',
    close: '<PERSON><PERSON>',
    open: '<PERSON><PERSON>',
    enable: '<PERSON><PERSON><PERSON>',
    disable: '<PERSON><PERSON><PERSON><PERSON>',
    show: '<PERSON><PERSON>',
    hide: 'Nasco<PERSON>',
    yes: 'S<PERSON>',
    no: 'No',
    loading: 'Caricamento...',
    error: 'Errore',
    success: 'Successo',
    warning: 'Avviso',
    info: 'Informazioni',
    create: 'Crea',
    enterName: 'Inserisci nome...',
    sidebar: 'Barra laterale',
    sidebarDescription: 'Barra laterale con impostazioni e contenuti aggiuntivi',
  },

  // Settings
  settings: {
    title: 'Impostazioni',
    description: 'Qui saranno le impostazioni dell\'estensione (tema, aspetto, opzioni, ecc.)',
    basicSettings: 'Impostazioni di base',
    clockSettings: 'Impostazioni orologio',
    searchSettings: 'Motore di ricerca',
    fastLinksSettings: 'Link rapidi',
    backgroundSettings: 'Sfondo',
    presetsSettings: 'Preimpostazioni',
    listsSettings: 'Impostazioni elenchi',

    // Basic settings
    language: 'Lingua dell\'interfaccia',
    exportSettings: 'Esporta',
    importSettings: 'Importa',
    theme: 'Tema',
    accentColor: 'Colore d\'accento',
    borderRadius: 'Raggio del bordo',
    cleanMode: 'Modalità pulita',
    cleanModeDescription: 'Nasconde i pulsanti di apertura elenco',
    colorScheme: 'Schema colori',
    borderRounding: 'Arrotondamento elementi',
    
    // Clock
    showClock: 'Mostra orologio',
    showSeconds: 'Mostra secondi',
    showDate: 'Mostra data',
    clockSize: 'Dimensione orologio',
    clockColor: 'Colore orologio e data',
    
    // Search
    showSearch: 'Mostra ricerca',
    searchEngine: 'Motore di ricerca',
    searchSize: 'Dimensione ricerca',
    searchBackgroundColor: 'Colore sfondo ricerca',
    searchBorderColor: 'Colore bordo ricerca',
    searchTextColor: 'Colore testo ricerca',
    searchBackdropBlur: 'Sfuocatura sfondo ricerca',
    showSearchEngine: 'Mostra motore di ricerca',
    backgroundBlur: 'Sfuocatura sfondo',
    
    // Quick links
    showFastLinks: 'Mostra link rapidi',
    fastLinksColumns: 'Numero di colonne',
    fastLinksSize: 'Dimensione link rapido',
    fastLinksBackdropBlur: 'Sfuocatura sfondo link rapido',
    addFastLink: 'Aggiungi link rapido',
    textColor: 'Colore testo titolo',
    backdropColor: 'Colore sfondo',
    iconBackgroundColor: 'Colore sfondo icona',
    colors: 'Colori',
    display: 'Visualizzazione',
    hideIcons: 'Nascondi icone',
    hideText: 'Nascondi testo',
    backgroundEffects: 'Effetti sfondo icona',

    // Lists
    showLists: 'Mostra elenchi di link',
    listsColumns: 'Numero di colonne',
    listsBackgroundColor: 'Colore sfondo elenchi',
    listsBackdropBlur: 'Sfuocatura sfondo elenchi',
    addNewList: 'Aggiungi nuovo elenco',
    listBackground: 'Sfondo elenco',
    backgroundColor: 'Colore sfondo',
    borderColor: 'Colore bordo',
    borderThickness: 'Spessore bordo',
    hideBorder: 'Nascondi bordo',
    hideBackground: 'Nascondi sfondo',
    separator: 'Separatore',
    separatorColor: 'Colore separatore',
    separatorThickness: 'Spessore separatore',
    hideSeparator: 'Nascondi separatore',
    colorsAndIcons: 'Colori e icone',
    titleColor: 'Colore titolo',
    linkColor: 'Colore link',
    hideLinkIcons: 'Nascondi icone link',
    listGrid: 'Griglia elenco',
    columnsCount: 'Numero di colonne',
    listManagement: 'Gestione elenchi',
    
    // Background
    backgroundType: 'Tipo di sfondo',
    solidColor: 'Colore solido',
    gradient: 'Sfumatura',
    image: 'Immagine',
    brightness: 'Luminosità',
    contrast: 'Contrasto',
    saturation: 'Saturazione',
    blur: 'Sfuocatura',
    hueRotate: 'Rotazione tonalità',
    sepia: 'Seppia',
    grayscale: 'Scala di grigi',
    invert: 'Inverti',
    shadowOverlay: 'Sovrapposizione ombra',
    parallaxEffect: 'Effetto parallasse',
    autoSwitch: 'Cambio automatico',
    switchInterval: 'Intervallo di cambio',
    addImages: 'Aggiungi immagini',
    uploadImages: 'Carica immagini',
    addImage: 'Aggiungi immagine',
    checking: 'Controllo...',
    addRandomPhoto: 'Aggiungi foto casuale da internet',
    parallaxDescription: 'L\'immagine di sfondo seguirà il movimento del mouse',
    shadowBottom: 'Ombra inferiore',
    shadowDescription: 'Ombra sfumata per una migliore visibilità dell\'elenco',
    intensity: 'Intensità:',
    height: 'Altezza:',
    gallery: 'Galleria',
    startAutoSwitch: 'Avvia cambio automatico',
    stopAutoSwitch: 'Interrompi cambio automatico',
    onLoad: 'Al caricamento',
    daily: 'Giornaliero',
    deleteImage: 'Elimina immagine',
    
    // Gradients
    gradientType: 'Tipo di sfumatura',
    addColor: 'Aggiungi colore',
    deleteColor: 'Elimina colore',
    direction: 'Direzione',
    position: 'Posizione',
    customCSS: 'Sfumatura CSS (opzionale)',
    customCSSDescription: 'Inserisci stringa sfumatura CSS da applicare al posto della configurazione manuale',
    backgroundFilters: 'Filtri sfondo',
    resetFilters: 'Ripristina filtri',
    expandFilters: 'Espandi filtri',
    collapseFilters: 'Comprimi filtri',
    color: 'Colore',
    filters: 'Filtri sfondo',
    font: 'Carattere',
    right: 'Destra',
    left: 'Sinistra',
    bottom: 'Basso',
    top: 'Alto',
    bottomRight: 'In basso a destra',
    bottomLeft: 'In basso a sinistra',
    topRight: 'In alto a destra',
    topLeft: 'In alto a sinistra',
    center: 'Centro',
    gradientDirection: 'Direzione sfumatura',
    gradientColor1: 'Colore 1',
    gradientColor2: 'Colore 2',
    gradientColor3: 'Colore 3',
    gradientColor4: 'Colore 4',
    gradientColor5: 'Colore 5',
    gradientColor6: 'Colore 6',
    customGradient: 'Sfumatura personalizzata',
    customGradientCSS: 'CSS personalizzato',
    gradientPreview: 'Anteprima',
    linear: 'Lineare',
    radial: 'Radiale',
    conic: 'Conico',
    toRight: 'A destra',
    toLeft: 'A sinistra',
    toBottom: 'In basso',
    toTop: 'In alto',
    toBottomRight: 'In basso a destra',
    toBottomLeft: 'In basso a sinistra',
    toTopRight: 'In alto a destra',
    toTopLeft: 'In alto a sinistra',
    circle: 'Cerchio',
    ellipse: 'Ellisse',
    fromCenter: 'Dal centro',
    
    // Image filters
    imageFilters: 'Filtri immagine',
    
    // Presets
    presets: 'Preimpostazioni',
    createPreset: 'Crea preimpostazione',
    presetName: 'Nome preimpostazione',
    noPresets: 'Nessuna preimpostazione creata. Crea la tua prima preimpostazione per passare rapidamente tra le impostazioni.',
    renamePreset: 'Rinomina preimpostazione',
    updatePreset: 'Aggiorna preimpostazione con impostazioni attuali',
    deletePreset: 'Elimina preimpostazione',
    createNewPreset: 'Crea nuova preimpostazione',
    presetDescription: 'La preimpostazione salverà le impostazioni attuali di colore, carattere e sfondo.',

    // Font categories
    fontCategories: {
      sansSerif: 'Sans Serif',
      serif: 'Serif',
      monospace: 'Monospazio',
      display: 'Display',
      handwriting: 'Scrittura a mano',
      pixel: 'Pixel',
      terminal: 'Terminale',
    },
    
    // Translations
    aiTranslationsDisclaimer: 'Tutte le traduzioni sono generate da AI',

    // Font names and categories
    fonts: {
      systemFont: 'Carattere di sistema',
      systemFonts: 'Caratteri di sistema',
      serifFonts: 'Caratteri Serif',
      monospaceFonts: 'Caratteri Monospazio',
      displayFonts: 'Caratteri Display',
      pixelFonts: 'Caratteri Pixel',
      terminalFonts: 'Caratteri Terminale',
      modernFonts: 'Caratteri moderni aggiuntivi',
      decorativeFonts: 'Caratteri decorativi aggiuntivi',
    },

    // Reset and export
    resetAllColors: 'Ripristina tutti i colori all\'accento',
    resetAllSettings: 'Ripristina tutte le impostazioni',
  },

  // Lists
  lists: {
    newList: 'Nuovo elenco',
    listName: 'Nome elenco',
    addLink: 'Aggiungi link',
    linkName: 'Nome link',
    linkUrl: 'URL link',
    editList: 'Modifica elenco',
    deleteList: 'Elimina elenco',
    listIcon: 'Icona elenco',
    listColor: 'Colore elenco',
    linkColor: 'Colore link',
    hideIcons: 'Nascondi icone link',
    openInNewWindow: 'Apri in nuova finestra',
    copyLink: 'Copia link',
    editLink: 'Modifica link',
    deleteLink: 'Elimina link',
    title: 'Titolo',
    url: 'URL',
    addNewList: 'Aggiungi elenco',
    iconColor: 'Colore icona',
  },

  // Quick links
  fastLinks: {
    newFastLink: 'Nuovo link rapido',
    fastLinkName: 'Nome link rapido',
    fastLinkUrl: 'URL',
    editFastLink: 'Modifica link rapido',
    deleteFastLink: 'Elimina link rapido',
    fastLinkColor: 'Colore link rapido',
  },

  // Search
  search: {
    placeholder: 'Cerca...',
    searchWith: 'Cerca con',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Cerca caratteri...',
    fontsNotFound: 'Caratteri non trovati',
    searchInGoogle: 'Cerca in Google...',
    searchInYandex: 'Cerca in Yandex...',
    searchInBing: 'Cerca in Bing...',
    searchInDuckDuckGo: 'Cerca in DuckDuckGo...',
    searchInYahoo: 'Cerca in Yahoo...',
    searchInBaidu: 'Cerca in Baidu...',
    searchInStartpage: 'Cerca in Startpage...',
    searchInSearX: 'Cerca in SearX...',
    searchInEcosia: 'Cerca in Ecosia...',
    searchInBrave: 'Cerca in Brave...',
  },

  // Errors
  errors: {
    invalidUrl: 'URL non valido',
    nameRequired: 'Il nome è obbligatorio',
    urlRequired: 'L\'URL è obbligatorio',
    fileUploadError: 'Errore nel caricamento del file',
    settingsImportError: 'Errore nell\'importazione delle impostazioni',
    settingsExportError: 'Errore nell\'esportazione delle impostazioni',
    invalidImageUrl: 'URL immagine non valido',
    imageValidationError: 'Errore di convalida immagine',
    imageLoadFailed: 'Impossibile caricare l\'immagine',
    imageTooSmall: 'L\'immagine è troppo piccola',
    settingsImported: 'Impostazioni importate con successo! La pagina verrà ricaricata per applicare tutte le modifiche.',
    settingsExported: 'Impostazioni esportate con successo',
    parseError: 'Errore di analisi:',
    invalidFileFormat: 'Errore nella lettura del file delle impostazioni. Assicurati che il file abbia il formato corretto.',
    importError: 'Errore durante l\'importazione delle impostazioni. Controlla la console per i dettagli.',
    exportError: 'Errore durante l\'esportazione delle impostazioni. Controlla la console per i dettagli.',
    resetConfirm: 'Sei sicuro di voler ripristinare tutte le impostazioni ai valori predefiniti? Questa azione non può essere annullata. Tutte le impostazioni personalizzate, inclusi sfondi, elenchi e link rapidi, verranno completamente rimosse dal localStorage.',
    deleteListConfirm: 'Sei sicuro di voler eliminare questo elenco? Questa azione non può essere annullata.',
    deleteLinkConfirm: 'Sei sicuro di voler eliminare questo link?',
    loadingError: 'Errore di caricamento',
    backgroundLoadError: 'Impossibile caricare l\'immagine di sfondo',
    criticalError: 'Si è verificato un errore critico. La pagina verrà ricaricata.',
    jsError: 'Errore JavaScript',
    promiseRejection: 'Errore Promise non gestito',
  },

  // Tooltips
  tooltips: {
    settings: 'Impostazioni',
    addList: 'Aggiungi elenco',
    addFastLink: 'Aggiungi link rapido',
    editItem: 'Modifica',
    deleteItem: 'Elimina',
    updateItem: 'Aggiorna',
    resetColor: 'Ripristina colore',
    openAllLinks: 'Apri tutti i link in nuove schede',
    addLink: 'Aggiungi link',
    openLink: 'Apri link',
    copyLink: 'Copia link',
    dragToReorder: 'Trascina per riordinare',
    exportSettings: 'Esporta tutte le impostazioni su file',
    importSettings: 'Importa impostazioni da file',
    resetAllSettings: 'Ripristina tutte le impostazioni',
    closeSettings: 'Chiudi impostazioni',
    generateColor: 'Genera colore in base all\'immagine',
    applyAccentColor: 'Applica colore d\'accento a tutti gli elementi',
    addRandomPhoto: 'Aggiungi foto casuale da internet',
    deleteImage: 'Elimina immagine',
    addColor: 'Aggiungi colore',
    deleteColor: 'Elimina colore',
    resetFilters: 'Ripristina filtri',
    expandFilters: 'Espandi filtri',
    collapseFilters: 'Comprimi filtri',
    stopAutoSwitch: 'Interrompi cambio automatico',
    startAutoSwitch: 'Avvia cambio automatico',
  },

  // Time
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'giorni',
    months: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
    weekdays: ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'],
  },

  // Units
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Piccolo',
    medium: 'Medio',
    large: 'Grande',
  },

  // Dialogs
  dialogs: {
    newName: 'Nuovo nome',
    newListName: 'Nome nuovo elenco',
    linkTitle: 'Titolo link',
    linkColor: 'Colore link',
    separatorColor: 'Colore separatore',
    titleColor: 'Colore titolo',
  },

  // ARIA labels
  ariaLabels: {
    settings: 'Impostazioni',
    resetAllSettings: 'Ripristina tutte le impostazioni',
    closeSettings: 'Chiudi impostazioni',
    applyAccentColor: 'Applica colore d\'accento a tutti gli elementi',
    addList: 'Aggiungi elenco',
    addRandomPhoto: 'Aggiungi foto casuale',
    deleteImage: 'Elimina immagine',
    addColor: 'Aggiungi colore',
    deleteColor: 'Elimina colore',
    resetFilters: 'Ripristina filtri',
    expandFilters: 'Espandi filtri',
    collapseFilters: 'Comprimi filtri',
  },

  // Border radius
  radius: {
    none: 'Nessuno',
    small: 'Piccolo',
    medium: 'Medio',
    large: 'Grande',
    full: 'Pieno',
  },

  // Font categories
  fonts: {
    system: 'Sistema',
    serif: 'Serif',
    monospace: 'Monospazio',
    display: 'Display',
    pixel: 'Pixel',
    terminal: 'Terminale',
    modern: 'Moderno',
    decorative: 'Decorativo',
  },
};
