export const vi = {
  // Chung
  common: {
    apply: 'Áp dụng',
    cancel: 'Hủy',
    save: '<PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON>',
    edit: 'Chỉnh sửa',
    update: 'Cập nhật',
    reset: 'Đặt lại',
    resetIcon: 'Đặt lại biểu tượng',
    add: 'Thêm',
    remove: '<PERSON>ó<PERSON>',
    close: 'Đóng',
    open: 'Mở',
    enable: 'B<PERSON><PERSON>',
    disable: 'Tắt',
    show: 'Hiển thị',
    hide: 'Ẩn',
    yes: 'C<PERSON>',
    no: 'Không',
    loading: '<PERSON>ang tải...',
    error: 'Lỗi',
    success: 'Thành công',
    warning: 'Cảnh báo',
    info: 'Thông tin',
    create: 'Tạo',
    enterName: 'Nhập tên...',
    sidebar: 'Thanh bên',
    sidebarDescription: 'Thanh bên với cài đặt và nội dung bổ sung',
  },

  // Cài đặt
  settings: {
    title: '<PERSON>ài đặt',
    description: '<PERSON><PERSON><PERSON> đây sẽ có các cài đặt tiện ích mở rộng (chủ đề, giao di<PERSON>, tù<PERSON> chọn, v.v.)',
    basicSettings: '<PERSON>à<PERSON> đặt cơ bản',
    clockSettings: 'Cài đặt đồng hồ',
    searchSettings: 'Công cụ tìm kiếm',
    fastLinksSettings: 'Liên kết nhanh',
    backgroundSettings: 'Nền',
    presetsSettings: 'Cài đặt trước',
    listsSettings: 'Cài đặt danh sách',

    // Cài đặt cơ bản
    language: 'Ngôn ngữ giao diện',
    exportSettings: 'Xuất',
    importSettings: 'Nhập',
    theme: 'Chủ đề',
    accentColor: 'Màu nhấn',
    borderRadius: 'Bán kính đường viền',
    cleanMode: 'Chế độ gọn gàng',
    cleanModeDescription: 'Ẩn các nút mở danh sách',
    colorScheme: 'Sơ đồ màu',
    borderRounding: 'Bo tròn các yếu tố',
    
    // Đồng hồ
    showClock: 'Hiển thị đồng hồ',
    showSeconds: 'Hiển thị giây',
    showDate: 'Hiển thị ngày',
    clockSize: 'Kích thước đồng hồ',
    clockColor: 'Màu đồng hồ và ngày',
    
    // Tìm kiếm
    showSearch: 'Hiển thị tìm kiếm',
    searchEngine: 'Công cụ tìm kiếm',
    searchSize: 'Kích thước tìm kiếm',
    searchBackgroundColor: 'Màu nền tìm kiếm',
    searchBorderColor: 'Màu viền tìm kiếm',
    searchTextColor: 'Màu chữ tìm kiếm',
    searchBackdropBlur: 'Làm mờ nền tìm kiếm',
    showSearchEngine: 'Hiển thị công cụ tìm kiếm',
    backgroundBlur: 'Làm mờ nền',
    
    // Liên kết nhanh
    showFastLinks: 'Hiển thị liên kết nhanh',
    fastLinksColumns: 'Số cột',
    fastLinksSize: 'Kích thước liên kết nhanh',
    fastLinksBackdropBlur: 'Làm mờ nền liên kết nhanh',
    addFastLink: 'Thêm liên kết nhanh',
    textColor: 'Màu chữ tiêu đề',
    backdropColor: 'Màu nền',
    iconBackgroundColor: 'Màu nền biểu tượng',
    colors: 'Màu sắc',
    display: 'Hiển thị',
    hideIcons: 'Ẩn biểu tượng',
    hideText: 'Ẩn chữ',
    backgroundEffects: 'Hiệu ứng nền biểu tượng',

    // Danh sách
    showLists: 'Hiển thị danh sách liên kết',
    listsColumns: 'Số cột',
    listsBackgroundColor: 'Màu nền danh sách',
    listsBackdropBlur: 'Làm mờ nền danh sách',
    addNewList: 'Thêm danh sách mới',
    listBackground: 'Nền danh sách',
    backgroundColor: 'Màu nền',
    borderColor: 'Màu viền',
    borderThickness: 'Độ dày viền',
    hideBorder: 'Ẩn viền',
    hideBackground: 'Ẩn nền',
    separator: 'Dải phân cách',
    separatorColor: 'Màu dải phân cách',
    separatorThickness: 'Độ dày dải phân cách',
    hideSeparator: 'Ẩn dải phân cách',
    colorsAndIcons: 'Màu sắc và biểu tượng',
    titleColor: 'Màu tiêu đề',
    linkColor: 'Màu liên kết',
    hideLinkIcons: 'Ẩn biểu tượng liên kết',
    listGrid: 'Lưới danh sách',
    columnsCount: 'Số lượng cột',
    listManagement: 'Quản lý danh sách',
    
    // Nền
    backgroundType: 'Loại nền',
    solidColor: 'Màu đơn sắc',
    gradient: 'Gradient',
    image: 'Hình ảnh',
    brightness: 'Độ sáng',
    contrast: 'Độ tương phản',
    saturation: 'Độ bão hòa',
    blur: 'Làm mờ',
    hueRotate: 'Xoay màu',
    sepia: 'Nâu đỏ',
    grayscale: 'Thang độ xám',
    invert: 'Đảo ngược',
    shadowOverlay: 'Lớp phủ bóng',
    parallaxEffect: 'Hiệu ứng thị sai',
    autoSwitch: 'Tự động chuyển đổi',
    switchInterval: 'Khoảng thời gian chuyển đổi',
    addImages: 'Thêm hình ảnh',
    uploadImages: 'Tải lên hình ảnh',
    addImage: 'Thêm hình ảnh',
    checking: 'Đang kiểm tra...',
    addRandomPhoto: 'Thêm ảnh ngẫu nhiên từ internet',
    parallaxDescription: 'Hình nền sẽ theo dõi chuyển động của chuột',
    shadowBottom: 'Bóng dưới',
    shadowDescription: 'Bóng đổ chuyển màu để dễ nhìn danh sách hơn',
    intensity: 'Cường độ:',
    height: 'Chiều cao:',
    gallery: 'Bộ sưu tập',
    startAutoSwitch: 'Bắt đầu chuyển đổi tự động',
    stopAutoSwitch: 'Dừng chuyển đổi tự động',
    onLoad: 'Khi tải',
    daily: 'Hàng ngày',
    deleteImage: 'Xóa hình ảnh',
    
    // Gradient
    gradientType: 'Loại Gradient',
    addColor: 'Thêm màu',
    deleteColor: 'Xóa màu',
    direction: 'Hướng',
    position: 'Vị trí',
    customCSS: 'Gradient CSS tùy chỉnh (tùy chọn)',
    customCSSDescription: 'Nhập chuỗi gradient CSS để áp dụng thay vì cấu hình thủ công',
    backgroundFilters: 'Bộ lọc nền',
    resetFilters: 'Đặt lại bộ lọc',
    expandFilters: 'Mở rộng bộ lọc',
    collapseFilters: 'Thu gọn bộ lọc',
    color: 'Màu',
    filters: 'Bộ lọc nền',
    font: 'Phông chữ',
    right: 'Phải',
    left: 'Trái',
    bottom: 'Dưới',
    top: 'Trên',
    bottomRight: 'Dưới cùng bên phải',
    bottomLeft: 'Dưới cùng bên trái',
    topRight: 'Trên cùng bên phải',
    topLeft: 'Trên cùng bên trái',
    center: 'Giữa',
    gradientDirection: 'Hướng Gradient',
    gradientColor1: 'Màu 1',
    gradientColor2: 'Màu 2',
    gradientColor3: 'Màu 3',
    gradientColor4: 'Màu 4',
    gradientColor5: 'Màu 5',
    gradientColor6: 'Màu 6',
    customGradient: 'Gradient tùy chỉnh',
    customGradientCSS: 'CSS tùy chỉnh',
    gradientPreview: 'Xem trước',
    linear: 'Tuyệt đối',
    radial: 'Hướng tâm',
    conic: 'Hình nón',
    toRight: 'Sang phải',
    toLeft: 'Sang trái',
    toBottom: 'Xuống dưới',
    toTop: 'Lên trên',
    toBottomRight: 'Xuống dưới bên phải',
    toBottomLeft: 'Xuống dưới bên trái',
    toTopRight: 'Lên trên bên phải',
    toTopLeft: 'Lên trên bên trái',
    circle: 'Vòng tròn',
    ellipse: 'Hình elip',
    fromCenter: 'Từ trung tâm',
    
    // Bộ lọc hình ảnh
    imageFilters: 'Bộ lọc hình ảnh',
    
    // Cài đặt trước
    presets: 'Cài đặt trước',
    createPreset: 'Tạo cài đặt trước',
    presetName: 'Tên cài đặt trước',
    noPresets: 'Chưa có cài đặt trước nào được tạo. Hãy tạo cài đặt trước đầu tiên của bạn để nhanh chóng chuyển đổi giữa các cài đặt.',
    renamePreset: 'Đổi tên cài đặt trước',
    updatePreset: 'Cập nhật cài đặt trước với cài đặt hiện tại',
    deletePreset: 'Xóa cài đặt trước',
    createNewPreset: 'Tạo cài đặt trước mới',
    presetDescription: 'Cài đặt trước sẽ lưu các cài đặt màu sắc, phông chữ và nền hiện tại.',

    // Danh mục phông chữ
    fontCategories: {
      sansSerif: 'Sans Serif',
      serif: 'Serif',
      monospace: 'Monospace',
      display: 'Hiển thị',
      handwriting: 'Chữ viết tay',
      pixel: 'Pixel',
      terminal: 'Thiết bị đầu cuối',
    },
    
    // Bản dịch
    aiTranslationsDisclaimer: 'Tất cả các bản dịch được tạo bởi AI',

    // Tên và danh mục phông chữ
    fonts: {
      systemFont: 'Phông chữ hệ thống',
      systemFonts: 'Phông chữ hệ thống',
      serifFonts: 'Phông chữ Serif',
      monospaceFonts: 'Phông chữ Monospace',
      displayFonts: 'Phông chữ hiển thị',
      pixelFonts: 'Phông chữ Pixel',
      terminalFonts: 'Phông chữ thiết bị đầu cuối',
      modernFonts: 'Phông chữ hiện đại bổ sung',
      decorativeFonts: 'Phông chữ trang trí bổ sung',
    },

    // Đặt lại và xuất
    resetAllColors: 'Đặt lại tất cả màu sắc về màu nhấn',
    resetAllSettings: 'Đặt lại tất cả cài đặt',
  },

  // Danh sách
  lists: {
    newList: 'Danh sách mới',
    listName: 'Tên danh sách',
    addLink: 'Thêm liên kết',
    linkName: 'Tên liên kết',
    linkUrl: 'URL liên kết',
    editList: 'Chỉnh sửa danh sách',
    deleteList: 'Xóa danh sách',
    listIcon: 'Biểu tượng danh sách',
    listColor: 'Màu danh sách',
    linkColor: 'Màu liên kết',
    hideIcons: 'Ẩn biểu tượng liên kết',
    openInNewWindow: 'Mở trong cửa sổ mới',
    copyLink: 'Sao chép liên kết',
    editLink: 'Chỉnh sửa liên kết',
    deleteLink: 'Xóa liên kết',
    title: 'Tiêu đề',
    url: 'URL',
    addNewList: 'Thêm danh sách',
    iconColor: 'Màu biểu tượng',
  },

  // Liên kết nhanh
  fastLinks: {
    newFastLink: 'Liên kết nhanh mới',
    fastLinkName: 'Tên liên kết nhanh',
    fastLinkUrl: 'URL',
    editFastLink: 'Chỉnh sửa liên kết nhanh',
    deleteFastLink: 'Xóa liên kết nhanh',
    fastLinkColor: 'Màu liên kết nhanh',
  },

  // Tìm kiếm
  search: {
    placeholder: 'Tìm kiếm...',
    searchWith: 'Tìm kiếm bằng',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Tìm kiếm phông chữ...',
    fontsNotFound: 'Không tìm thấy phông chữ',
    searchInGoogle: 'Tìm kiếm trong Google...',
    searchInYandex: 'Tìm kiếm trong Yandex...',
    searchInBing: 'Tìm kiếm trong Bing...',
    searchInDuckDuckGo: 'Tìm kiếm trong DuckDuckGo...',
    searchInYahoo: 'Tìm kiếm trong Yahoo...',
    searchInBaidu: 'Tìm kiếm trong Baidu...',
    searchInStartpage: 'Tìm kiếm trong Startpage...',
    searchInSearX: 'Tìm kiếm trong SearX...',
    searchInEcosia: 'Tìm kiếm trong Ecosia...',
    searchInBrave: 'Tìm kiếm trong Brave...',
  },

  // Lỗi
  errors: {
    invalidUrl: 'URL không hợp lệ',
    nameRequired: 'Tên là bắt buộc',
    urlRequired: 'URL là bắt buộc',
    fileUploadError: 'Lỗi tải lên tệp',
    settingsImportError: 'Lỗi nhập cài đặt',
    settingsExportError: 'Lỗi xuất cài đặt',
    invalidImageUrl: 'URL hình ảnh không hợp lệ',
    imageValidationError: 'Lỗi xác thực hình ảnh',
    imageLoadFailed: 'Không thể tải hình ảnh',
    imageTooSmall: 'Hình ảnh quá nhỏ',
    settingsImported: 'Cài đặt đã được nhập thành công! Trang sẽ được tải lại để áp dụng tất cả các thay đổi.',
    settingsExported: 'Cài đặt đã được xuất thành công',
    parseError: 'Lỗi phân tích cú pháp:',
    invalidFileFormat: 'Lỗi khi đọc tệp cài đặt. Đảm bảo tệp có định dạng chính xác.',
    importError: 'Lỗi khi nhập cài đặt. Kiểm tra console để biết chi tiết.',
    exportError: 'Lỗi khi xuất cài đặt. Kiểm tra console để biết chi tiết.',
    resetConfirm: 'Bạn có chắc chắn muốn đặt lại tất cả cài đặt về giá trị mặc định không? Hành động này không thể hoàn tác. Tất cả các cài đặt tùy chỉnh, bao gồm hình nền, danh sách và liên kết nhanh, sẽ bị xóa hoàn toàn khỏi bộ nhớ cục bộ.',
    deleteListConfirm: 'Bạn có chắc chắn muốn xóa danh sách này không? Hành động này không thể hoàn tác.',
    deleteLinkConfirm: 'Bạn có chắc chắn muốn xóa liên kết này không?',
    loadingError: 'Lỗi tải',
    backgroundLoadError: 'Không thể tải hình nền',
    criticalError: 'Đã xảy ra lỗi nghiêm trọng. Trang sẽ được tải lại.',
    jsError: 'Lỗi JavaScript',
    promiseRejection: 'Lỗi Promise chưa được xử lý',
  },

  // Chú giải công cụ
  tooltips: {
    settings: 'Cài đặt',
    addList: 'Thêm danh sách',
    addFastLink: 'Thêm liên kết nhanh',
    editItem: 'Chỉnh sửa',
    deleteItem: 'Xóa',
    updateItem: 'Cập nhật',
    resetColor: 'Đặt lại màu',
    openAllLinks: 'Mở tất cả các liên kết trong tab mới',
    addLink: 'Thêm liên kết',
    openLink: 'Mở liên kết',
    copyLink: 'Sao chép liên kết',
    dragToReorder: 'Kéo để sắp xếp lại',
    exportSettings: 'Xuất tất cả cài đặt ra tệp',
    importSettings: 'Nhập cài đặt từ tệp',
    resetAllSettings: 'Đặt lại tất cả cài đặt',
    closeSettings: 'Đóng cài đặt',
    generateColor: 'Tạo màu dựa trên hình ảnh',
    applyAccentColor: 'Áp dụng màu nhấn cho tất cả các yếu tố',
    addRandomPhoto: 'Thêm ảnh ngẫu nhiên từ internet',
    deleteImage: 'Xóa hình ảnh',
    addColor: 'Thêm màu',
    deleteColor: 'Xóa màu',
    resetFilters: 'Đặt lại bộ lọc',
    expandFilters: 'Mở rộng bộ lọc',
    collapseFilters: 'Thu gọn bộ lọc',
    stopAutoSwitch: 'Dừng chuyển đổi tự động',
    startAutoSwitch: 'Bắt đầu chuyển đổi tự động',
  },

  // Thời gian
  time: {
    seconds: 'giây',
    minutes: 'phút',
    hours: 'giờ',
    days: 'ngày',
    months: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
    weekdays: ['Chủ Nhật', 'Thứ Hai', 'Thứ Ba', 'Thứ Tư', 'Thứ Năm', 'Thứ Sáu', 'Thứ Bảy'],
  },

  // Đơn vị
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'giây',
    minutes: 'phút',
    small: 'Nhỏ',
    medium: 'Trung bình',
    large: 'Lớn',
  },

  // Hộp thoại
  dialogs: {
    newName: 'Tên mới',
    newListName: 'Tên danh sách mới',
    linkTitle: 'Tiêu đề liên kết',
    linkColor: 'Màu liên kết',
    separatorColor: 'Màu dải phân cách',
    titleColor: 'Màu tiêu đề',
  },

  // Nhãn ARIA
  ariaLabels: {
    settings: 'Cài đặt',
    resetAllSettings: 'Đặt lại tất cả cài đặt',
    closeSettings: 'Đóng cài đặt',
    applyAccentColor: 'Áp dụng màu nhấn cho tất cả các yếu tố',
    addList: 'Thêm danh sách',
    addRandomPhoto: 'Thêm ảnh ngẫu nhiên',
    deleteImage: 'Xóa hình ảnh',
    addColor: 'Thêm màu',
    deleteColor: 'Xóa màu',
    resetFilters: 'Đặt lại bộ lọc',
    expandFilters: 'Mở rộng bộ lọc',
    collapseFilters: 'Thu gọn bộ lọc',
  },

  // Bán kính đường viền
  radius: {
    none: 'Không',
    small: 'Nhỏ',
    medium: 'Trung bình',
    large: 'Lớn',
    full: 'Đầy đủ',
  },

  // Danh mục phông chữ
  fonts: {
    system: 'Hệ thống',
    serif: 'Serif',
    monospace: 'Monospace',
    display: 'Hiển thị',
    pixel: 'Pixel',
    terminal: 'Thiết bị đầu cuối',
    modern: 'Hiện đại',
    decorative: 'Trang trí',
  },
};