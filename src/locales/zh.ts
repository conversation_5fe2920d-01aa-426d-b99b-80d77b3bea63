// 中文翻译词典
export const zh = {
  // 通用
  common: {
    apply: '应用',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    update: '更新',
    reset: '重置',
    resetIcon: '重置图标',
    add: '添加',
    remove: '移除',
    close: '关闭',
    open: '打开',
    enable: '启用',
    disable: '禁用',
    show: '显示',
    hide: '隐藏',
    yes: '是',
    no: '否',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    warning: '警告',
    info: '信息',
    create: '创建',
    enterName: '输入名称...',
    sidebar: '侧边栏',
    sidebarDescription: '包含设置和附加内容的侧边栏',
  },

  // 设置
  settings: {
    title: '设置',
    description: '此处为扩展设置（主题、外观、选项等）',
    basicSettings: '基本设置',
    clockSettings: '时钟设置',
    searchSettings: '搜索引擎',
    fastLinksSettings: '快捷链接',
    backgroundSettings: '背景',
    presetsSettings: '预设',
    listsSettings: '列表设置',

    // 基本设置
    language: '界面语言',
    exportSettings: '导出',
    importSettings: '导入',
    theme: '主题',
    accentColor: '强调色',
    borderRadius: '圆角半径',
    cleanMode: '简洁模式',
    cleanModeDescription: '隐藏列表展开按钮',
    colorScheme: '配色方案',
    borderRounding: '元素圆角',
    
    // 时钟
    showClock: '显示时钟',
    showSeconds: '显示秒数',
    showDate: '显示日期',
    clockSize: '时钟大小',
    clockColor: '时钟和日期颜色',
    
    // 搜索
    showSearch: '显示搜索框',
    searchEngine: '搜索引擎',
    searchSize: '搜索框大小',
    searchBackgroundColor: '搜索框背景色',
    searchBorderColor: '搜索框边框色',
    searchTextColor: '搜索框文字颜色',
    searchBackdropBlur: '搜索框背景模糊',
    showSearchEngine: '显示搜索引擎标识',
    backgroundBlur: '背景模糊',
    
    // 快捷链接
    showFastLinks: '显示快捷链接',
    fastLinksColumns: '列数',
    fastLinksSize: '快捷链接大小',
    fastLinksBackdropBlur: '快捷链接背景模糊',
    addFastLink: '添加快捷链接',
    textColor: '标题文字颜色',
    backdropColor: '背景颜色',
    iconBackgroundColor: '图标背景色',
    colors: '颜色',
    display: '显示',
    hideIcons: '隐藏图标',
    hideText: '隐藏文字',
    backgroundEffects: '图标背景效果',

    // 列表
    showLists: '显示链接列表',
    listsColumns: '列数',
    listsBackgroundColor: '列表背景色',
    listsBackdropBlur: '列表背景模糊',
    addNewList: '添加新列表',
    listBackground: '列表背景',
    backgroundColor: '背景颜色',
    borderColor: '边框颜色',
    borderThickness: '边框粗细',
    hideBorder: '隐藏边框',
    hideBackground: '隐藏背景',
    separator: '分隔线',
    separatorColor: '分隔线颜色',
    separatorThickness: '分隔线粗细',
    hideSeparator: '隐藏分隔线',
    colorsAndIcons: '颜色和图标',
    titleColor: '标题颜色',
    linkColor: '链接颜色',
    hideLinkIcons: '隐藏链接图标',
    listGrid: '列表网格',
    columnsCount: '列数',
    listManagement: '列表管理',
    
    // 背景
    backgroundType: '背景类型',
    solidColor: '纯色',
    gradient: '渐变',
    image: '图片',
    brightness: '亮度',
    contrast: '对比度',
    saturation: '饱和度',
    blur: '模糊',
    hueRotate: '色相旋转',
    sepia: '复古色',
    grayscale: '灰度',
    invert: '反色',
    shadowOverlay: '阴影覆盖',
    parallaxEffect: '视差效果',
    autoSwitch: '自动切换',
    switchInterval: '切换间隔',
    addImages: '添加图片',
    uploadImages: '上传图片',
    addImage: '添加图片',
    checking: '检查中...',
    addRandomPhoto: '添加随机网络图片',
    parallaxDescription: '背景图片将跟随鼠标移动',
    shadowBottom: '底部阴影',
    shadowDescription: '渐变阴影提升列表可见性',
    intensity: '强度:',
    height: '高度:',
    gallery: '图库',
    startAutoSwitch: '开始自动切换',
    stopAutoSwitch: '停止自动切换',
    onLoad: '加载时',
    daily: '每日',
    deleteImage: '删除图片',
    
    // 渐变
    gradientType: '渐变类型',
    addColor: '添加颜色',
    deleteColor: '删除颜色',
    direction: '方向',
    position: '位置',
    customCSS: 'CSS渐变(可选)',
    customCSSDescription: '输入CSS渐变代码代替手动设置',
    backgroundFilters: '背景滤镜',
    resetFilters: '重置滤镜',
    expandFilters: '展开滤镜',
    collapseFilters: '收起滤镜',
    color: '颜色',
    filters: '背景滤镜',
    font: '字体',
    right: '右',
    left: '左',
    bottom: '下',
    top: '上',
    bottomRight: '右下',
    bottomLeft: '左下',
    topRight: '右上',
    topLeft: '左上',
    center: '中',
    gradientDirection: '渐变方向',
    gradientColor1: '颜色1',
    gradientColor2: '颜色2',
    gradientColor3: '颜色3',
    gradientColor4: '颜色4',
    gradientColor5: '颜色5',
    gradientColor6: '颜色6',
    customGradient: '自定义渐变',
    customGradientCSS: '自定义CSS',
    gradientPreview: '预览',
    linear: '线性',
    radial: '径向',
    conic: '锥形',
    toRight: '向右',
    toLeft: '向左',
    toBottom: '向下',
    toTop: '向上',
    toBottomRight: '向右下',
    toBottomLeft: '向左下',
    toTopRight: '向右上',
    toTopLeft: '向左上',
    circle: '圆形',
    ellipse: '椭圆',
    fromCenter: '从中心',
    
    // 图片滤镜
    imageFilters: '图片滤镜',
    
    // 预设
    presets: '预设',
    createPreset: '创建预设',
    presetName: '预设名称',
    noPresets: '未创建预设。创建第一个预设以快速切换设置。',
    renamePreset: '重命名预设',
    updatePreset: '使用当前设置更新预设',
    deletePreset: '删除预设',
    createNewPreset: '创建新预设',
    presetDescription: '预设将保存当前颜色、字体和背景设置。',

    // 字体分类
    fontCategories: {
      sansSerif: '无衬线体',
      serif: '衬线体',
      monospace: '等宽字体',
      display: '展示字体',
      handwriting: '手写体',
      pixel: '像素字体',
      terminal: '终端字体',
    },
    
    // 翻译说明
    aiTranslationsDisclaimer: '所有翻译均为AI生成',

    // 字体名称和分类
    fonts: {
      systemFont: '系统字体',
      systemFonts: '系统字体',
      serifFonts: '衬线字体',
      monospaceFonts: '等宽字体',
      displayFonts: '展示字体',
      pixelFonts: '像素字体',
      terminalFonts: '终端字体',
      modernFonts: '附加现代字体',
      decorativeFonts: '附加装饰字体',
    },

    // 重置和导出
    resetAllColors: '将所有颜色重置为强调色',
    resetAllSettings: '重置所有设置',
  },

  // 列表
  lists: {
    newList: '新建列表',
    listName: '列表名称',
    addLink: '添加链接',
    linkName: '链接名称',
    linkUrl: '链接URL',
    editList: '编辑列表',
    deleteList: '删除列表',
    listIcon: '列表图标',
    listColor: '列表颜色',
    linkColor: '链接颜色',
    hideIcons: '隐藏链接图标',
    openInNewWindow: '在新窗口打开',
    copyLink: '复制链接',
    editLink: '编辑链接',
    deleteLink: '删除链接',
    title: '标题',
    url: 'URL',
    addNewList: '添加列表',
    iconColor: '图标颜色',
  },

  // 快捷链接
  fastLinks: {
    newFastLink: '新建快捷链接',
    fastLinkName: '快捷链接名称',
    fastLinkUrl: 'URL',
    editFastLink: '编辑快捷链接',
    deleteFastLink: '删除快捷链接',
    fastLinkColor: '快捷链接颜色',
  },

  // 搜索
  search: {
    placeholder: '搜索...',
    searchWith: '使用',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: '搜索字体...',
    fontsNotFound: '未找到字体',
    searchInGoogle: '在Google搜索...',
    searchInYandex: '在Yandex搜索...',
    searchInBing: '在Bing搜索...',
    searchInDuckDuckGo: '在DuckDuckGo搜索...',
    searchInYahoo: '在Yahoo搜索...',
    searchInBaidu: '在百度搜索...',
    searchInStartpage: '在Startpage搜索...',
    searchInSearX: '在SearX搜索...',
    searchInEcosia: '在Ecosia搜索...',
    searchInBrave: '在Brave搜索...',
  },

  // 错误
  errors: {
    invalidUrl: '无效URL',
    nameRequired: '需要名称',
    urlRequired: '需要URL',
    fileUploadError: '文件上传错误',
    settingsImportError: '设置导入错误',
    settingsExportError: '设置导出错误',
    invalidImageUrl: '无效图片URL',
    imageValidationError: '图片验证错误',
    imageLoadFailed: '图片加载失败',
    imageTooSmall: '图片尺寸过小',
    settingsImported: '设置导入成功! 页面将重新加载以应用更改。',
    settingsExported: '设置导出成功',
    parseError: '解析错误:',
    invalidFileFormat: '读取设置文件错误。请确保文件格式正确。',
    importError: '导入设置错误。请查看控制台获取详情。',
    exportError: '导出设置错误。请查看控制台获取详情。',
    resetConfirm: '确定要重置所有设置为默认值吗？此操作不可逆。所有自定义设置(包括背景、列表和快捷链接)将从localStorage完全删除。',
    deleteListConfirm: '确定要删除此列表吗？此操作不可逆。',
    deleteLinkConfirm: '确定要删除此链接吗？',
    loadingError: '加载错误',
    backgroundLoadError: '背景图片加载失败',
    criticalError: '发生严重错误。页面将重新加载。',
    jsError: 'JavaScript错误',
    promiseRejection: '未处理的Promise错误',
  },

  // 工具提示
  tooltips: {
    settings: '设置',
    addList: '添加列表',
    addFastLink: '添加快捷链接',
    editItem: '编辑',
    deleteItem: '删除',
    updateItem: '更新',
    resetColor: '重置颜色',
    openAllLinks: '在新标签页打开所有链接',
    addLink: '添加链接',
    openLink: '打开链接',
    copyLink: '复制链接',
    dragToReorder: '拖拽重新排序',
    exportSettings: '导出所有设置到文件',
    importSettings: '从文件导入设置',
    resetAllSettings: '重置所有设置',
    closeSettings: '关闭设置',
    generateColor: '基于图片生成颜色',
    applyAccentColor: '将强调色应用到所有元素',
    addRandomPhoto: '添加随机网络图片',
    deleteImage: '删除图片',
    addColor: '添加颜色',
    deleteColor: '删除颜色',
    resetFilters: '重置滤镜',
    expandFilters: '展开滤镜',
    collapseFilters: '收起滤镜',
    stopAutoSwitch: '停止自动切换',
    startAutoSwitch: '开始自动切换',
  },

  // 时间
  time: {
    seconds: '秒',
    minutes: '分',
    hours: '小时',
    days: '天',
    months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
    weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
  },

  // 单位
  units: {
    pixels: '像素',
    percent: '%',
    seconds: '秒',
    minutes: '分',
    small: '小',
    medium: '中',
    large: '大',
  },

  // 对话框
  dialogs: {
    newName: '新名称',
    newListName: '新列表名称',
    linkTitle: '链接标题',
    linkColor: '链接颜色',
    separatorColor: '分隔线颜色',
    titleColor: '标题颜色',
  },

  // ARIA标签
  ariaLabels: {
    settings: '设置',
    resetAllSettings: '重置所有设置',
    closeSettings: '关闭设置',
    applyAccentColor: '将强调色应用到所有元素',
    addList: '添加列表',
    addRandomPhoto: '添加随机图片',
    deleteImage: '删除图片',
    addColor: '添加颜色',
    deleteColor: '删除颜色',
    resetFilters: '重置滤镜',
    expandFilters: '展开滤镜',
    collapseFilters: '收起滤镜',
  },

  // 圆角
  radius: {
    none: '无',
    small: '小',
    medium: '中',
    large: '大',
    full: '全圆角',
  },

  // 字体分类
  fonts: {
    system: '系统',
    serif: '衬线',
    monospace: '等宽',
    display: '展示',
    pixel: '像素',
    terminal: '终端',
    modern: '现代',
    decorative: '装饰',
  },
};