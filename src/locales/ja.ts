// 日本語翻訳辞書
export const ja = {
  // 一般
  common: {
    apply: '適用',
    cancel: 'キャンセル',
    save: '保存',
    delete: '削除',
    edit: '編集',
    update: '更新',
    reset: 'リセット',
    resetIcon: 'アイコンをリセット',
    add: '追加',
    remove: '削除',
    close: '閉じる',
    open: '開く',
    enable: '有効',
    disable: '無効',
    show: '表示',
    hide: '非表示',
    yes: 'はい',
    no: 'いいえ',
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    warning: '警告',
    info: '情報',
    create: '作成',
    enterName: '名前を入力...',
    sidebar: 'サイドバー',
    sidebarDescription: '設定や追加コンテンツを含むサイドバー',
  },

  // 設定
  settings: {
    title: '設定',
    description: '拡張機能の設定（テーマ、外観、オプションなど）',
    basicSettings: '基本設定',
    clockSettings: '時計設定',
    searchSettings: '検索エンジン',
    fastLinksSettings: 'クイックリンク',
    backgroundSettings: '背景',
    presetsSettings: 'プリセット',
    listsSettings: 'リスト設定',

    // 基本設定
    language: 'インターフェース言語',
    exportSettings: 'エクスポート',
    importSettings: 'インポート',
    theme: 'テーマ',
    accentColor: 'アクセントカラー',
    borderRadius: '角丸半径',
    cleanMode: 'クリーンモード',
    cleanModeDescription: 'リスト開閉ボタンを非表示',
    colorScheme: '配色',
    borderRounding: '要素の丸み',
    
    // 時計
    showClock: '時計を表示',
    showSeconds: '秒を表示',
    showDate: '日付を表示',
    clockSize: '時計のサイズ',
    clockColor: '時計と日付の色',
    
    // 検索
    showSearch: '検索を表示',
    searchEngine: '検索エンジン',
    searchSize: '検索サイズ',
    searchBackgroundColor: '検索背景色',
    searchBorderColor: '検索枠線色',
    searchTextColor: '検索文字色',
    searchBackdropBlur: '検索背景ぼかし',
    showSearchEngine: '検索エンジンを表示',
    backgroundBlur: '背景ぼかし',
    
    // クイックリンク
    showFastLinks: 'クイックリンクを表示',
    fastLinksColumns: '列数',
    fastLinksSize: 'クイックリンクサイズ',
    fastLinksBackdropBlur: 'クイックリンク背景ぼかし',
    addFastLink: 'クイックリンクを追加',
    textColor: 'タイトル文字色',
    backdropColor: '背景色',
    iconBackgroundColor: 'アイコン背景色',
    colors: '色',
    display: '表示',
    hideIcons: 'アイコンを非表示',
    hideText: 'テキストを非表示',
    backgroundEffects: 'アイコン背景効果',

    // リスト
    showLists: 'リンクリストを表示',
    listsColumns: '列数',
    listsBackgroundColor: 'リスト背景色',
    listsBackdropBlur: 'リスト背景ぼかし',
    addNewList: '新規リストを追加',
    listBackground: 'リスト背景',
    backgroundColor: '背景色',
    borderColor: '枠線色',
    borderThickness: '枠線の太さ',
    hideBorder: '枠線を非表示',
    hideBackground: '背景を非表示',
    separator: '区切り線',
    separatorColor: '区切り線の色',
    separatorThickness: '区切り線の太さ',
    hideSeparator: '区切り線を非表示',
    colorsAndIcons: '色とアイコン',
    titleColor: 'タイトル色',
    linkColor: 'リンク色',
    hideLinkIcons: 'リンクアイコンを非表示',
    listGrid: 'リストグリッド',
    columnsCount: '列数',
    listManagement: 'リスト管理',
    
    // 背景
    backgroundType: '背景タイプ',
    solidColor: '単色',
    gradient: 'グラデーション',
    image: '画像',
    brightness: '明るさ',
    contrast: 'コントラスト',
    saturation: '彩度',
    blur: 'ぼかし',
    hueRotate: '色相回転',
    sepia: 'セピア',
    grayscale: 'グレースケール',
    invert: '反転',
    shadowOverlay: 'シャドウオーバーレイ',
    parallaxEffect: 'パララックス効果',
    autoSwitch: '自動切り替え',
    switchInterval: '切り替え間隔',
    addImages: '画像を追加',
    uploadImages: '画像をアップロード',
    addImage: '画像を追加',
    checking: '確認中...',
    addRandomPhoto: 'ランダムな写真を追加',
    parallaxDescription: '背景画像がマウス移動に追従',
    shadowBottom: '下部シャドウ',
    shadowDescription: 'リストの視認性向上のためのグラデーションシャドウ',
    intensity: '強度:',
    height: '高さ:',
    gallery: 'ギャラリー',
    startAutoSwitch: '自動切り替えを開始',
    stopAutoSwitch: '自動切り替えを停止',
    onLoad: '読み込み時',
    daily: '毎日',
    deleteImage: '画像を削除',
    
    // グラデーション
    gradientType: 'グラデーションタイプ',
    addColor: '色を追加',
    deleteColor: '色を削除',
    direction: '方向',
    position: '位置',
    customCSS: 'CSSグラデーション（オプション）',
    customCSSDescription: '手動設定の代わりにCSSグラデーション文字列を入力',
    backgroundFilters: '背景フィルター',
    resetFilters: 'フィルターをリセット',
    expandFilters: 'フィルターを展開',
    collapseFilters: 'フィルターを折りたたむ',
    color: '色',
    filters: '背景フィルター',
    font: 'フォント',
    right: '右',
    left: '左',
    bottom: '下',
    top: '上',
    bottomRight: '右下',
    bottomLeft: '左下',
    topRight: '右上',
    topLeft: '左上',
    center: '中央',
    gradientDirection: 'グラデーション方向',
    gradientColor1: '色1',
    gradientColor2: '色2',
    gradientColor3: '色3',
    gradientColor4: '色4',
    gradientColor5: '色5',
    gradientColor6: '色6',
    customGradient: 'カスタムグラデーション',
    customGradientCSS: 'カスタムCSS',
    gradientPreview: 'プレビュー',
    linear: '線形',
    radial: '放射状',
    conic: '円錐',
    toRight: '右へ',
    toLeft: '左へ',
    toBottom: '下へ',
    toTop: '上へ',
    toBottomRight: '右下へ',
    toBottomLeft: '左下へ',
    toTopRight: '右上へ',
    toTopLeft: '左上へ',
    circle: '円形',
    ellipse: '楕円',
    fromCenter: '中心から',
    
    // 画像フィルター
    imageFilters: '画像フィルター',
    
    // プリセット
    presets: 'プリセット',
    createPreset: 'プリセットを作成',
    presetName: 'プリセット名',
    noPresets: 'プリセットがありません。設定を素早く切り替えるために最初のプリセットを作成してください。',
    renamePreset: 'プリセット名を変更',
    updatePreset: '現在の設定でプリセットを更新',
    deletePreset: 'プリセットを削除',
    createNewPreset: '新規プリセットを作成',
    presetDescription: 'プリセットは現在の色、フォント、背景設定を保存します。',

    // フォントカテゴリ
    fontCategories: {
      sansSerif: 'サンセリフ',
      serif: 'セリフ',
      monospace: '等幅',
      display: 'ディスプレイ',
      handwriting: '手書き',
      pixel: 'ピクセル',
      terminal: 'ターミナル',
    },
    
    // 翻訳
    aiTranslationsDisclaimer: 'すべての翻訳はAI生成',

    // フォント名とカテゴリ
    fonts: {
      systemFont: 'システムフォント',
      systemFonts: 'システムフォント',
      serifFonts: 'セリフフォント',
      monospaceFonts: '等幅フォント',
      displayFonts: 'ディスプレイフォント',
      pixelFonts: 'ピクセルフォント',
      terminalFonts: 'ターミナルフォント',
      modernFonts: '追加モダンフォント',
      decorativeFonts: '追加装飾フォント',
    },

    // リセットとエクスポート
    resetAllColors: 'すべての色をアクセントにリセット',
    resetAllSettings: 'すべての設定をリセット',
  },

  // リスト
  lists: {
    newList: '新規リスト',
    listName: 'リスト名',
    addLink: 'リンクを追加',
    linkName: 'リンク名',
    linkUrl: 'リンクURL',
    editList: 'リストを編集',
    deleteList: 'リストを削除',
    listIcon: 'リストアイコン',
    listColor: 'リストの色',
    linkColor: 'リンクの色',
    hideIcons: 'リンクアイコンを非表示',
    openInNewWindow: '新しいウィンドウで開く',
    copyLink: 'リンクをコピー',
    editLink: 'リンクを編集',
    deleteLink: 'リンクを削除',
    title: 'タイトル',
    url: 'URL',
    addNewList: 'リストを追加',
    iconColor: 'アイコン色',
  },

  // クイックリンク
  fastLinks: {
    newFastLink: '新規クイックリンク',
    fastLinkName: 'クイックリンク名',
    fastLinkUrl: 'URL',
    editFastLink: 'クイックリンクを編集',
    deleteFastLink: 'クイックリンクを削除',
    fastLinkColor: 'クイックリンクの色',
  },

  // 検索
  search: {
    placeholder: '検索...',
    searchWith: '検索エンジン',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'フォントを検索...',
    fontsNotFound: 'フォントが見つかりません',
    searchInGoogle: 'Googleで検索...',
    searchInYandex: 'Yandexで検索...',
    searchInBing: 'Bingで検索...',
    searchInDuckDuckGo: 'DuckDuckGoで検索...',
    searchInYahoo: 'Yahooで検索...',
    searchInBaidu: 'Baiduで検索...',
    searchInStartpage: 'Startpageで検索...',
    searchInSearX: 'SearXで検索...',
    searchInEcosia: 'Ecosiaで検索...',
    searchInBrave: 'Braveで検索...',
  },

  // エラー
  errors: {
    invalidUrl: '無効なURL',
    nameRequired: '名前が必要です',
    urlRequired: 'URLが必要です',
    fileUploadError: 'ファイルアップロードエラー',
    settingsImportError: '設定インポートエラー',
    settingsExportError: '設定エクスポートエラー',
    invalidImageUrl: '無効な画像URL',
    imageValidationError: '画像検証エラー',
    imageLoadFailed: '画像読み込み失敗',
    imageTooSmall: '画像が小さすぎます',
    settingsImported: '設定のインポートに成功しました！変更を適用するためにページを再読み込みします。',
    settingsExported: '設定のエクスポートに成功しました',
    parseError: '解析エラー:',
    invalidFileFormat: '設定ファイルの読み込みエラー。正しい形式のファイルか確認してください。',
    importError: '設定のインポートエラー。詳細はコンソールを確認してください。',
    exportError: '設定のエクスポートエラー。詳細はコンソールを確認してください。',
    resetConfirm: 'すべての設定をデフォルト値にリセットしますか？この操作は取り消せません。背景、リスト、クイックリンクを含むすべてのカスタム設定がlocalStorageから完全に削除されます。',
    deleteListConfirm: 'このリストを削除しますか？この操作は取り消せません。',
    deleteLinkConfirm: 'このリンクを削除しますか？',
    loadingError: '読み込みエラー',
    backgroundLoadError: '背景画像の読み込みに失敗',
    criticalError: '重大なエラーが発生しました。ページを再読み込みします。',
    jsError: 'JavaScriptエラー',
    promiseRejection: '未処理のPromiseエラー',
  },

  // ツールチップ
  tooltips: {
    settings: '設定',
    addList: 'リストを追加',
    addFastLink: 'クイックリンクを追加',
    editItem: '編集',
    deleteItem: '削除',
    updateItem: '更新',
    resetColor: '色をリセット',
    openAllLinks: 'すべてのリンクを新しいタブで開く',
    addLink: 'リンクを追加',
    openLink: 'リンクを開く',
    copyLink: 'リンクをコピー',
    dragToReorder: 'ドラッグで並べ替え',
    exportSettings: 'すべての設定をファイルにエクスポート',
    importSettings: 'ファイルから設定をインポート',
    resetAllSettings: 'すべての設定をリセット',
    closeSettings: '設定を閉じる',
    generateColor: '画像に基づいて色を生成',
    applyAccentColor: 'アクセントカラーをすべての要素に適用',
    addRandomPhoto: 'ランダムな写真を追加',
    deleteImage: '画像を削除',
    addColor: '色を追加',
    deleteColor: '色を削除',
    resetFilters: 'フィルターをリセット',
    expandFilters: 'フィルターを展開',
    collapseFilters: 'フィルターを折りたたむ',
    stopAutoSwitch: '自動切り替えを停止',
    startAutoSwitch: '自動切り替えを開始',
  },

  // 時間
  time: {
    seconds: '秒',
    minutes: '分',
    hours: '時間',
    days: '日',
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    weekdays: ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
  },

  // 単位
  units: {
    pixels: 'px',
    percent: '%',
    seconds: '秒',
    minutes: '分',
    small: '小',
    medium: '中',
    large: '大',
  },

  // ダイアログ
  dialogs: {
    newName: '新しい名前',
    newListName: '新しいリスト名',
    linkTitle: 'リンクタイトル',
    linkColor: 'リンクの色',
    separatorColor: '区切り線の色',
    titleColor: 'タイトル色',
  },

  // ARIAラベル
  ariaLabels: {
    settings: '設定',
    resetAllSettings: 'すべての設定をリセット',
    closeSettings: '設定を閉じる',
    applyAccentColor: 'アクセントカラーをすべての要素に適用',
    addList: 'リストを追加',
    addRandomPhoto: 'ランダムな写真を追加',
    deleteImage: '画像を削除',
    addColor: '色を追加',
    deleteColor: '色を削除',
    resetFilters: 'フィルターをリセット',
    expandFilters: 'フィルターを展開',
    collapseFilters: 'フィルターを折りたたむ',
  },

  // 角丸
  radius: {
    none: 'なし',
    small: '小',
    medium: '中',
    large: '大',
    full: '完全',
  },

  // フォントカテゴリ
  fonts: {
    system: 'システム',
    serif: 'セリフ',
    monospace: '等幅',
    display: 'ディスプレイ',
    pixel: 'ピクセル',
    terminal: 'ターミナル',
    modern: 'モダン',
    decorative: '装飾',
  },
};